<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vote extends Model
{
    use HasFactory;

    protected $fillable = [
        'agent_id',
        'candidate_id',
        'number_of_votes',
        'latitude',
        'longitude'
    ];

    /**
     * Get the agent that owns the vote
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the candidate that owns the vote
     */
    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }
}

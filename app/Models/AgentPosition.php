<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentPosition extends Model
{
    use HasFactory;

    public static function saveAgentPosition($agent_id,$position_id){

        $saveAgentPosition = new AgentPosition();

        $saveAgentPosition->agent_id = $agent_id;

        $saveAgentPosition->position_id = $position_id;

        $saveAgentPosition->save();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VoteAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'agent_id',
        'candidate_id',
        'polling_station_id',
        'position_id',
        'previous_votes',
        'new_votes',
        'vote_difference',
        'action_type',
        'submission_method',
        'ip_address',
        'user_agent',
        'latitude',
        'longitude',
        'submitted_by_user_id',
        'submitted_by_user_type',
        'submission_time',
        'notes',
        'is_verified',
        'verified_by_user_id',
        'verified_at',
        'verification_notes',
        'is_flagged',
        'flag_reason',
        'flag_notes',
        'flagged_by_user_id',
        'flagged_at'
    ];

    protected $casts = [
        'submission_time' => 'datetime',
        'verified_at' => 'datetime',
        'flagged_at' => 'datetime',
        'is_verified' => 'boolean',
        'is_flagged' => 'boolean',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7'
    ];

    /**
     * Get the agent that made the submission
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the candidate for this vote submission
     */
    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    /**
     * Get the polling station
     */
    public function pollingStation(): BelongsTo
    {
        return $this->belongsTo(PollingStation::class);
    }

    /**
     * Get the position
     */
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    /**
     * Get the user who submitted the votes
     */
    public function submittedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by_user_id');
    }

    /**
     * Get the user who verified the submission
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by_user_id');
    }

    /**
     * Get the user who flagged the submission
     */
    public function flaggedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'flagged_by_user_id');
    }

    /**
     * Scope for flagged submissions
     */
    public function scopeFlagged($query)
    {
        return $query->where('is_flagged', true);
    }

    /**
     * Scope for verified submissions
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for unverified submissions
     */
    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    /**
     * Scope for submissions within a time range
     */
    public function scopeWithinTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('submission_time', [$startTime, $endTime]);
    }

    /**
     * Scope for submissions by agent
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * Scope for submissions by polling station
     */
    public function scopeByPollingStation($query, $pollingStationId)
    {
        return $query->where('polling_station_id', $pollingStationId);
    }

    /**
     * Get submissions that might be suspicious
     */
    public function scopeSuspicious($query)
    {
        return $query->where(function($q) {
            $q->where('is_flagged', true)
              ->orWhere('vote_difference', '>', 1000) // Large vote changes
              ->orWhereRaw('ABS(vote_difference) > new_votes * 0.5'); // Changes > 50% of total
        });
    }

    /**
     * Get recent submissions (last 24 hours)
     */
    public function scopeRecent($query)
    {
        return $query->where('submission_time', '>=', Carbon::now()->subDay());
    }

    /**
     * Get multiple submissions for the same candidate by the same agent
     */
    public function scopeMultipleSubmissions($query)
    {
        return $query->selectRaw('agent_id, candidate_id, COUNT(*) as submission_count')
                    ->groupBy('agent_id', 'candidate_id')
                    ->havingRaw('COUNT(*) > 1');
    }

    /**
     * Check if this submission should be automatically flagged
     */
    public function shouldBeFlagged(): array
    {
        $flags = [];

        // Check for rapid multiple submissions (more than 3 in 10 minutes)
        $recentSubmissions = self::where('agent_id', $this->agent_id)
            ->where('candidate_id', $this->candidate_id)
            ->where('submission_time', '>=', Carbon::now()->subMinutes(10))
            ->count();

        if ($recentSubmissions > 3) {
            $flags[] = 'multiple_rapid_submissions';
        }

        // Check for large vote changes (more than 1000 votes difference)
        if (abs($this->vote_difference) > 1000) {
            $flags[] = 'large_vote_change';
        }

        // Check for unusual timing (submissions outside 6 AM - 10 PM)
        $hour = $this->submission_time->hour;
        if ($hour < 6 || $hour > 22) {
            $flags[] = 'unusual_timing';
        }

        return $flags;
    }

    /**
     * Auto-flag this submission if it meets suspicious criteria
     */
    public function autoFlag(): void
    {
        $flags = $this->shouldBeFlagged();
        
        if (!empty($flags)) {
            $this->update([
                'is_flagged' => true,
                'flag_reason' => $flags[0], // Use the first flag reason
                'flag_notes' => 'Auto-flagged: ' . implode(', ', $flags),
                'flagged_at' => now()
            ]);
        }
    }

    /**
     * Get formatted submission time
     */
    public function getFormattedSubmissionTimeAttribute(): string
    {
        return $this->submission_time->format('M d, Y H:i:s');
    }

    /**
     * Get vote change description
     */
    public function getVoteChangeDescriptionAttribute(): string
    {
        if ($this->action_type === 'create') {
            return "Initial submission: {$this->new_votes} votes";
        }

        $change = $this->vote_difference;
        if ($change > 0) {
            return "Increased by {$change} votes (from {$this->previous_votes} to {$this->new_votes})";
        } elseif ($change < 0) {
            return "Decreased by " . abs($change) . " votes (from {$this->previous_votes} to {$this->new_votes})";
        } else {
            return "No change in vote count ({$this->new_votes} votes)";
        }
    }

    /**
     * Get status badge color for UI
     */
    public function getStatusBadgeColorAttribute(): string
    {
        if ($this->is_flagged) {
            return 'danger';
        } elseif ($this->is_verified) {
            return 'success';
        } else {
            return 'warning';
        }
    }

    /**
     * Get status text for UI
     */
    public function getStatusTextAttribute(): string
    {
        if ($this->is_flagged) {
            return 'Flagged';
        } elseif ($this->is_verified) {
            return 'Verified';
        } else {
            return 'Pending';
        }
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please log in to access this page.');
        }

        $user = Auth::user();

        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            return redirect()->route('login')->with('error', 'Your account has been deactivated. Please contact an administrator.');
        }

        // Admin users bypass all role checks
        if ($user->user_type === 'admin') {
            return $next($request);
        }

        // Check if user has any of the required roles
        if (!$user->hasAnyRole($roles)) {
            // Determine appropriate redirect based on user type
            $redirectUrl = $this->getRedirectUrl($user);

            return redirect($redirectUrl)->with('error', 'You do not have the required role to access that page.');
        }

        return $next($request);
    }

    /**
     * Get the appropriate redirect URL based on user type
     */
    private function getRedirectUrl($user): string
    {
        switch ($user->user_type) {
            case 'agent':
                return '/agent_dashboard';
            case 'manager':
                return '/manager/dashboard';
            case 'viewer':
            case 'admin':
            default:
                return '/home';
        }
    }
}

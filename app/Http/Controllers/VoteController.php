<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Vote;
use App\Services\VoteAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /** 
     * Record Vote
     * @group Votes 
     * @bodyParam candidate_id integer required
     * @bodyParam number_of_votes integer required
     * @bodyParam latitude double
     * @bodyParam longitude double
     * @response   {
     *       "status": "success",
     *       "message": "Vote Saved successfully"
     *   }
    **/
    public function store(Request $request)
    {
        $rules = [
            'candidate_id'=>'required',
            'number_of_votes'=>'required|numeric|min:0',
            'latitude'=>'nullable|numeric',
            'longitude'=>'nullable|numeric',
        ];

        $this->validate($request,$rules);

        $agent = Agent::where('user_id',Auth::id())->first();

        if(!$agent){
            return response()->json(['status' => 'failed', 'message' => 'Only Agents can post Election results'],422);
        }

        DB::beginTransaction();
        try {
            // Get existing vote record
            $existingVote = Vote::where('agent_id', $agent->id)
                ->where('candidate_id', $request->candidate_id)
                ->first();

            $actionType = $existingVote ? 'update' : 'create';

            // Log the submission in audit trail BEFORE updating the vote
            $auditService = new VoteAuditService();
            $auditService->logVoteSubmission(
                $agent->id,
                $request->candidate_id,
                $request->number_of_votes,
                $actionType,
                'api', // Since this is API endpoint
                $request,
                "Vote submitted via mobile app"
            );

            // Create or update vote record AFTER logging
            $vote = Vote::updateOrCreate(
                [
                    'agent_id' => $agent->id,
                    'candidate_id' => $request->candidate_id
                ],
                [
                    'number_of_votes' => $request->number_of_votes,
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude,
                ]
            );

            DB::commit();

            if($request->ajax()){
                return response()->json([
                    'status' => 'success',
                    'message' => 'Vote saved and logged successfully',
                    'data' => $vote,
                    'action' => $actionType
                ], 200);
            }

            return back()->with('success', 'Vote saved successfully');

        } catch (\Exception $e) {
            DB::rollback();

            if($request->ajax()){
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Failed to save vote: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Failed to save vote');
        }
    }

    /**
     * Show the combined vote submission form for agents
     */
    public function showSubmitAllForm()
    {
        // Debug information
        $user = Auth::user();
        if (!$user) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        $agent = Agent::where('user_id', Auth::id())->first();

        if (!$agent) {
            // For debugging, let's see what user type we have
            return response()->json([
                'error' => 'Only agents can access this form.',
                'user_type' => $user->user_type,
                'user_id' => $user->id,
                'agent_found' => false
            ], 403);
        }

        $positions = \App\Models\Position::with('candidates')->get();
        $station = $agent->polling_station;

        // Get existing votes for this agent
        $existingVotes = [];
        $existingEvidence = collect();

        if ($agent) {
            $existingVotes = Vote::where('agent_id', $agent->id)
                ->pluck('number_of_votes', 'candidate_id')
                ->toArray();

            // Get existing evidence for this agent
            $existingEvidence = $agent->eveidences;
        }

        return view('agent.submit-all-votes', compact('agent', 'positions', 'station', 'existingVotes', 'existingEvidence'));
    }

    /**
     * Submit all votes and evidence for an agent
     */
    public function submitAllVotes(Request $request)
    {
        $request->validate([
            'votes' => 'required|array',
            'votes.*' => 'required|integer|min:0',
            'evidence_files.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'file_names.*' => 'nullable|string|max:255',
        ]);

        $agent = Agent::where('user_id', Auth::id())->first();

        if (!$agent) {
            return redirect()->back()->with('error', 'Only agents can submit votes.');
        }

        DB::beginTransaction();
        try {
            $auditService = new VoteAuditService();
            $uploadedFiles = [];

            // Process vote submissions
            foreach ($request->votes as $candidateId => $voteCount) {
                // Get existing vote to determine action type
                $existingVote = Vote::where('agent_id', $agent->id)
                    ->where('candidate_id', $candidateId)
                    ->first();

                $actionType = $existingVote ? 'update' : 'create';

                // Log the submission in audit trail BEFORE updating the vote
                $auditService->logVoteSubmission(
                    $agent->id,
                    $candidateId,
                    $voteCount,
                    $actionType,
                    'agent_portal',
                    $request,
                    "All votes submitted by agent for {$agent->polling_station->name}"
                );

                // Update or create vote record AFTER logging
                Vote::updateOrCreate(
                    [
                        'agent_id' => $agent->id,
                        'candidate_id' => $candidateId
                    ],
                    [
                        'number_of_votes' => $voteCount,
                        'latitude' => $agent->polling_station->latitude,
                        'longitude' => $agent->polling_station->longitude,
                    ]
                );
            }

            // Process evidence uploads if any files are provided
            if ($request->hasFile('evidence_files')) {
                foreach ($request->file('evidence_files') as $index => $file) {
                    if ($file && $file->isValid()) {
                        $fileName = $request->file_names[$index] ?? 'Evidence ' . ($index + 1);

                        $evidence = new \App\Models\Eveidence();
                        $evidence->file_url = \App\Models\User::uploadImage($file);
                        $evidence->file_name = $fileName;
                        $evidence->agent_id = $agent->id;
                        $evidence->save();

                        $uploadedFiles[] = $fileName;
                    }
                }
            }

            DB::commit();

            // Create success message
            $message = "All votes submitted successfully for {$agent->polling_station->name}";
            if (!empty($uploadedFiles)) {
                $message .= ". Evidence uploaded: " . implode(', ', $uploadedFiles);
            }

            return redirect()->route('home')->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Failed to submit votes and evidence. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Vote $vote)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vote $vote)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vote $vote)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vote $vote)
    {
        //
    }
}

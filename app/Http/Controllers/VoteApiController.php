<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\AgentPosition;
use App\Models\Position;
use App\Models\User;
use App\Models\Vote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class VoteApiController extends Controller
{
    /** 
     * Log In  User
     * @group Authentication  
     * @bodyParam phone_number string required The phone number of the user
     * @bodyParam password string required The password of the user
     * @response  {
     *   "status": "success",
     *   "message": "Successfully loged in",
     *   "authorization": {
     *       "token": "4|VEAsGQHfQJyi7eNn7oRSqkuQpM4H8XhZQM6mdLOneb965ff7",
     *       "token_type": "Bearer"
     *   },
     *   "user": [
     *       {
     *           "id": 2,
     *           "name": "<PERSON><PERSON><PERSON>",
     *           "phone_number": "256787444081",
     *           "email_verified_at": null,
     *           "user_type": "agent",
     *           "created_at": "2025-07-05T12:21:51.000000Z",
     *           "updated_at": "2025-07-05T12:22:09.000000Z",
     *           "agent": {
     *               "id": 2,
     *               "created_at": "2025-07-05T12:22:09.000000Z",
     *               "updated_at": "2025-07-05T12:22:09.000000Z",
     *               "user_id": 2,
     *               "polling_station_id": 2,
     *               "polling_station": {
     *                   "id": 2,
     *                   "created_at": "2025-07-05T12:19:37.000000Z",
     *                   "updated_at": "2025-07-05T12:19:37.000000Z",
     *                   "name": "KATALE",
     *                   "constituency": null
     *               }
     *           }
     *       }
     *   ]
     *   }
     **/

    function login(Request $request){

        $rules = [
            'phone_number' => 'required',
            'password' => 'required'
        ];

        $message = User::getValidationMessage($request,$rules);

        if(!empty($message))
    
            return  response()->json(['status' => 'failed', 'message' => $message],422);   
            
        
        $credentials = $request->only('phone_number', 'password');

        if (Auth::attempt($credentials)) {
            
            // $user = Auth::user();       

            $user = User::with('agent','agent.polling_station')->where('id',Auth::id())->get();

            $token = Auth::user()->createToken('token', ['*'], now()->addWeek())->plainTextToken;

            $data = [
                'status' => 'success', 
                'message'=>'Successfully loged in',
                'authorization'=>[
                    'token'=>$token,
                    'token_type' => 'Bearer',
                ]  ,
                'user'=>$user,             
            ];
            
            return  response()->json($data,200);

        }

        if(empty($user))
    
            return  response()->json(['status' => 'failed', 'message' => 'Invalid phone number or password'],422);   
    }



    /** 
     * Logged In User
     * @group Authentication 
     * 
     * @response  {
     *       "status": "success",
     *       "message": "Logged in user",
     *       "user": {
     *           "id": 1,
     *           "name": "Kawalya Paul",
     *           "phone_number": "0785297660",
     *           "email_verified_at": null,
     *           "user_type": "admin",
     *           "created_at": "2025-07-04T06:40:37.000000Z",
     *           "updated_at": "2025-07-04T06:40:37.000000Z"
     *       }
     *   }
     **/
    function user() {

        $user = User::with('agents','agents.polling_station')->where('id',Auth::id())->get();

        $data = [
            'status' => 'success', 
            'message'=>'Logged in user',             
            'user'=>$user,             
        ];
        
        return  response()->json($data,200);
        
    }
    
    /** 
     * Change password
     * @group Authentication 
     * @bodyParam old_password string required
     * @bodyParam password string required
     * @bodyParam password_confirmation string required
     * @response  {
     *      "status": "success",
     *       "message": "Password changes"
     *   } 
    **/

    function changePassword(Request $request){

        $rules = [
            'old_password' => 'required',
            'password' => 'required|string|confirmed',
        ];

        $message = User::getValidationMessage($request,$rules);

        if(!empty($message))
    
            return  response()->json(['status' => 'failed', 'message' => $message],422);
            
            
        if (Hash::check($request->old_password, Auth::user()->password)) {

            $user = Auth::user();

            $user->password = Hash::make($request->old_password);

            $user->save();

            return  response()->json(['status' => 'success', 'message' => 'Password changes'],200);
         
        } 
     
        return  response()->json(['status' => 'failed', 'message' => 'Old password is wrong'],422);
    
    }


    /** 
     * Candidates
     * @group Votes 
     * @response   {
     *       "status": "success",
     *       "message": "Candidates",
     *       "positions": [
     *           {
     *               "id": 1,
     *               "name": "MP-KAWEMPE NORTH",
     *               "candidates": [
     *                   {
     *                       "id": 1,
     *                       "name": "NALUKOLA",
     *                       "photo": "1751717917mjUshH1MBFvU.jpg",
     *                       "party_name": "NUP",
     *                       "total_votes": "52"
     *                   },
     *                   {
     *                       "id": 2,
     *                       "name": "NAMBI",
     *                       "photo": "1751717945SEtuSlYttHQx.jpg",
     *                       "party_name": "NRM",
     *                       "total_votes": "56"
     *                   }
     *               ]
     *           }
     *       ]
     *   }
    **/

    function loadCandidates(){    
 
        
        $agent = Agent::where('user_id', Auth::id())->latest()->first(); // get latest agent for the logged-in user

        $agentPosition = AgentPosition::where('agent_id',$agent->id)->pluck('position_id')->toArray();

        $positions = Position::with('candidates')->whereIn('id',$agentPosition)->get(); // eager-load candidates

        $results = [];

        foreach ($positions as $position) {
            $positionData = [
                'id' => $position->id,
                'name' => $position->name,
                'candidates' => []
            ];

            foreach ($position->candidates as $candidate) {
                $total_votes = Vote::where('agent_id', $agent->id)
                    ->where('candidate_id', $candidate->id)
                    ->sum('number_of_votes');

                $candidateData = [
                    'id' => $candidate->id,
                    'name' => $candidate->name,
                    'photo' => $candidate->picture,
                    'party_name'=>$candidate->party_name,
                    'total_votes' => $total_votes,
                ];

                $positionData['candidates'][] = $candidateData;
            }

            $results[] = $positionData;
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Candidates',
            'positions' => $results,
        ], 200);
    }
 


    /** 
     * Agent Logout
     * @group Authentication  
    **/

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'status' => 'success', 
            'message' => 'Logged out successfully'
        ]);
    }

    /** 
     * View Candidate vot by agent
     * @group Votes 
     * @bodyParam candidate_id integer required
     * @response  {
     *   "status": "success",
     *   "message": "Candidates",
     *   "vote": {
     *       "id": 2,
     *       "created_at": "2025-07-05T09:22:04.000000Z",
     *       "updated_at": "2025-07-05T09:22:04.000000Z",
     *       "agent_id": 1,
     *       "candidate_id": 2,
     *       "number_of_votes": 23,
     *       "latitude": 56.45333,
     *       "longitude": 0.324545
     *   }
     *   }
    **/

    function candidateVotes(Request $request){ 
        
        $agent = Agent::where('user_id',Auth::id())->get()->last();

        if(!$agent){       

            return  response()->json(['status' => 'failed', 'message' => 'Only Agents can see results'],422);

        }

        $vote_record = Vote::where('agent_id',$agent->id)->where('candidate_id',$request->candidate_id)->get()->last();

        $data = [
            'status' => 'success', 
            'message'=>'Candidate vote',             
            'vote'=>$vote_record,             
        ];
        
        return  response()->json($data,200);
        
    }

   
  
}

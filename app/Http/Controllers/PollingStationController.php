<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\Vote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PollingStationController extends Controller
{
    /**
     * Check if coordinates are within Uganda
     *
     * @param float|null $latitude
     * @param float|null $longitude
     * @return bool
     */
    private function isWithinUganda($latitude, $longitude)
    {
        // If either coordinate is null, skip validation
        if ($latitude === null || $longitude === null) {
            return true;
        }
        
        // Uganda's approximate bounding box
        $ugandaBounds = [
            'minLat' => -1.5, // Southern border
            'maxLat' => 4.3,  // Northern border
            'minLng' => 29.5, // Western border
            'maxLng' => 35.0  // Eastern border
        ];
        
        return $latitude >= $ugandaBounds['minLat'] && 
               $latitude <= $ugandaBounds['maxLat'] && 
               $longitude >= $ugandaBounds['minLng'] && 
               $longitude <= $ugandaBounds['maxLng'];
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Permission check is handled by middleware

        // Build polling stations query with filters
        $pollingStationsQuery = PollingStation::with(['agents.user']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $pollingStationsQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('district', 'like', "%{$search}%")
                      ->orWhere('county', 'like', "%{$search}%")
                      ->orWhere('subcounty', 'like', "%{$search}%")
                      ->orWhere('parish', 'like', "%{$search}%")
                      ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($request->filled('district')) {
            $pollingStationsQuery->where('district', $request->get('district'));
        }

        if ($request->filled('county')) {
            $pollingStationsQuery->where('county', $request->get('county'));
        }

        if ($request->filled('subcounty')) {
            $pollingStationsQuery->where('subcounty', $request->get('subcounty'));
        }

        if ($request->filled('coordinates')) {
            if ($request->get('coordinates') === 'with') {
                $pollingStationsQuery->whereNotNull('latitude')->whereNotNull('longitude');
            } elseif ($request->get('coordinates') === 'without') {
                $pollingStationsQuery->where(function($query) {
                    $query->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        if ($request->filled('agents')) {
            if ($request->get('agents') === 'with') {
                $pollingStationsQuery->whereHas('agents');
            } elseif ($request->get('agents') === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents');
            }
        }

        if ($request->filled('results')) {
            if ($request->get('results') === 'submitted') {
                $pollingStationsQuery->whereHas('agents.votes');
            } elseif ($request->get('results') === 'pending') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.votes');
            }
        }

        if ($request->filled('evidence')) {
            if ($request->get('evidence') === 'with') {
                $pollingStationsQuery->whereHas('agents.eveidences');
            } elseif ($request->get('evidence') === 'without') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.eveidences');
            }
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 20);
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'district', 'county', 'subcounty', 'created_at'])) {
            $pollingStationsQuery->orderBy($sortBy, $sortOrder);
        } else {
            $pollingStationsQuery->orderBy('name', 'asc');
        }

        // Paginate results
        $polling_stations = $pollingStationsQuery->paginate($perPage)->appends($request->query());

        // Get filter options for dropdowns
        $districts = PollingStation::distinct()->whereNotNull('district')->pluck('district')->sort();
        $counties = PollingStation::distinct()->whereNotNull('county')->pluck('county')->sort();
        $subcounties = PollingStation::distinct()->whereNotNull('subcounty')->pluck('subcounty')->sort();

        // Get statistics
        $totalStations = PollingStation::count();
        $stationsWithCoordinates = PollingStation::whereNotNull('latitude')->whereNotNull('longitude')->count();
        $stationsWithAgents = PollingStation::whereHas('agents')->count();
        $stationsWithResults = PollingStation::whereHas('agents.votes')->count();

        $data = [
            'polling_stations' => $polling_stations,
            'districts' => $districts,
            'counties' => $counties,
            'subcounties' => $subcounties,
            'totalStations' => $totalStations,
            'stationsWithCoordinates' => $stationsWithCoordinates,
            'stationsWithAgents' => $stationsWithAgents,
            'stationsWithResults' => $stationsWithResults,
            'title' => 'Polling Stations',
            'currentFilters' => $request->only(['search', 'district', 'county', 'subcounty', 'coordinates', 'agents', 'results', 'evidence', 'per_page', 'sort_by', 'sort_order'])
        ];

        return view('polling_stations.list')->with($data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Permission check is handled by middleware, no need for additional checks here
        $data = [
            'title' => 'Create Polling Station'
        ];

        return view('polling_stations.create')->with($data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => 'required',
            'district' => 'required',
            'county' => 'required',
            'subcounty' => 'required',
            'parish' => 'required',
            'village' => 'required',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ];

        // Validate request
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            // Handle AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if coordinates are within Uganda
        if ($request->filled('latitude') && $request->filled('longitude')) {
            $latitude = $request->latitude;
            $longitude = $request->longitude;

            if (!$this->isWithinUganda($latitude, $longitude)) {
                $errorMessage = 'The coordinates must be within Uganda.';

                // Handle AJAX requests
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage,
                        'errors' => ['coordinates' => [$errorMessage]]
                    ], 422);
                }

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['coordinates' => $errorMessage]);
            }
        }

        $savePollingStation = new PollingStation();
        $savePollingStation->name = $request->name;
        $savePollingStation->constituency = $request->constituency;
        $savePollingStation->district = $request->district;
        $savePollingStation->county = $request->county;
        $savePollingStation->subcounty = $request->subcounty;
        $savePollingStation->parish = $request->parish;
        $savePollingStation->village = $request->village;
        $savePollingStation->latitude = $request->latitude;
        $savePollingStation->longitude = $request->longitude;

        $savePollingStation->save();

        // Handle AJAX requests (for modal)
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Polling station created successfully.',
                'station' => $savePollingStation
            ]);
        }

        return redirect()->route('polling_stations.index')->with('success', 'Polling station created successfully.');

    }

    /**
     * Display the specified resource.
     */
    public function show(PollingStation $pollingStation)
    {
        // Permission check is handled by middleware

        $positions = Position::get();

        $agents = Agent::with('user')->where('polling_station_id',$pollingStation->id)->get();

        $data = [
            'agents'=>$agents,
            'title'=>'Agents in '.$pollingStation->name,
            'pollingStation'=>$pollingStation,
            'positions'=>$positions,
        ];

        return view('polling_stations.agents')->with($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PollingStation $pollingStation)
    {
        $data = [
            'pollingStation'=>$pollingStation,
            'title'=>'Edit Polling Station'
        ];

        return view('polling_stations.edit_polling_station')->with($data);


    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $pollingStation_id)
    {
        $rules = [
            'name' => 'required',
            'district' => 'required',
            'county' => 'required',
            'subcounty' => 'required',
            'parish' => 'required',
            'village' => 'required',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ];

        $this->validate($request, $rules);

        // Check if coordinates are within Uganda
        if ($request->filled('latitude') && $request->filled('longitude')) {
            $latitude = $request->latitude;
            $longitude = $request->longitude;

            if (!$this->isWithinUganda($latitude, $longitude)) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['coordinates' => 'The coordinates must be within Uganda.']);
            }
        }

        $savePollingStation = PollingStation::findOrFail($pollingStation_id);
        $savePollingStation->name = $request->name;
        $savePollingStation->constituency = $request->constituency;
        $savePollingStation->district = $request->district;
        $savePollingStation->county = $request->county;
        $savePollingStation->subcounty = $request->subcounty;
        $savePollingStation->parish = $request->parish;
        $savePollingStation->village = $request->village;
        $savePollingStation->latitude = $request->latitude;
        $savePollingStation->longitude = $request->longitude;

        $savePollingStation->save();

        return redirect()->route('polling_stations.index')->with('success', 'Polling station updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($pollingStation)
    {
        try {
            PollingStation::destroy($pollingStation);
        } catch (\Throwable $th) {}

        return back();
    }

    /**
     * Get polling stations data for map display
     */
    public function getMapData()
    {
        $polling_stations = PollingStation::with('agents')->get();
        $positions = Position::with('candidates')->get();

        $mapData = [];

        foreach ($polling_stations as $station) {
            $stationData = [
                'id' => $station->id,
                'name' => $station->name,
                'latitude' => $station->latitude ?: (0.3476 + (rand(-100, 100) / 1000)), // Default to Uganda area with some randomness
                'longitude' => $station->longitude ?: (32.5825 + (rand(-200, 200) / 1000)),
                'votes' => []
            ];

            // Calculate votes for each candidate at this station
            foreach ($positions as $position) {
                foreach ($position->candidates as $candidate) {
                    $votes = Vote::join('agents', 'votes.agent_id', '=', 'agents.id')
                        ->where('agents.polling_station_id', $station->id)
                        ->where('votes.candidate_id', $candidate->id)
                        ->sum('votes.number_of_votes') ?? 0;

                    $stationData['votes'][$candidate->id] = $votes;
                }
            }

            $mapData[] = $stationData;
        }

        return response()->json($mapData);
    }

    /**
     * Get evidence files for a specific polling station
     */
    public function getStationEvidence($stationId)
    {
        try {
            $pollingStation = PollingStation::with(['agent.eveidences'])->findOrFail($stationId);

            if (!$pollingStation->agent) {
                return response()->json([
                    'success' => false,
                    'message' => 'No agent assigned to this polling station',
                    'evidence' => [],
                    'station_name' => $pollingStation->name
                ]);
            }

            $evidence = $pollingStation->agent->eveidences->map(function($evidence) {
                return [
                    'id' => $evidence->id,
                    'file_url' => $evidence->file_url,
                    'file_name' => $evidence->file_name ?: 'Evidence_' . $evidence->id,
                    'created_at' => $evidence->created_at->format('Y-m-d H:i:s')
                ];
            });

            return response()->json([
                'success' => true,
                'evidence' => $evidence,
                'station_name' => $pollingStation->name,
                'agent_name' => $pollingStation->agent->user->name,
                'count' => $evidence->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch evidence: ' . $e->getMessage(),
                'evidence' => []
            ], 500);
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Eveidence;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EveidenceController extends Controller
{
    /** 
     * View Evedence
     * @group Votes 
     * @bodyParam picture file required
     * @bodyParam file_name string
     * @response   {
     *   "status": "success",
     *   "message": "Uploaded files",
     *   "evedence": [
     *       {
     *           "id": 1,
     *           "created_at": "2025-07-05T09:43:49.000000Z",
     *           "updated_at": "2025-07-05T09:43:49.000000Z",
     *           "agent_id": 1,
     *           "file_url": "1751708629XsEueTFKd760.jpg",
     *           "file_name": "DR FORM"
     *       }
     *   ]
     * }
    **/
    public function index()
    {
        $agent = Agent::where('user_id',Auth::id())->get()->last();

        if(!$agent){
            return  response()->json(['status' => 'failed', 'message' => 'Only Agents can view their own results'],422);
        }

        $results = Eveidence::where('agent_id',$agent->id)->get();

           $data = [
            'status' => 'success', 
            'message'=>'Uploaded files',             
            'evedence'=>$results,             
        ];
        
        return  response()->json($data,200);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /** 
     * Store Evedence
     * @group Votes 
     * @bodyParam picture file required
     * @bodyParam file_name string
     * @response  {
     *       "status": "success",
     *       "message": "Evendance uploaded successfully",
     *       "evedence": {
     *           "file_url": "1751708629XsEueTFKd760.jpg",
     *           "file_name": "DR FORM",
     *           "agent_id": 1,
     *           "updated_at": "2025-07-05T09:43:49.000000Z",
     *           "created_at": "2025-07-05T09:43:49.000000Z",
     *           "id": 1
     *       }
     *   }
    **/

    public function store(Request $request)
    {

        $rules = [
            'picture'=>'required',
            'file_name'=>'nullable',            
        ];

        $this->validate($request,$rules);   

        $agent = Agent::where('user_id',Auth::id())->get()->last();

        if(!$agent){
            return  response()->json(['status' => 'failed', 'message' => 'Only Agents can post results'],422);
        }

        $evedence = new Eveidence();
        
        $evedence->file_url =  User::uploadImage($request->file('picture'));

        $evedence->file_name = $request->file_name;

        $evedence->agent_id = $agent->id;

        $evedence->save();

      if (!$request->is('api/*')) return back();

        $data = [
            'status' => 'success', 
            'message'=>'Evendance uploaded successfully',             
            'evedence'=>$evedence,             
        ];
        
        return  response()->json($data,200);

    }

    /**
     * Display the specified resource.
     */
    public function show(Eveidence $eveidence)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Eveidence $eveidence)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Eveidence $eveidence)
    {
        //
    }

    /** 
     * Delete Evedence
     * @group Votes 
     * @urlParam eveidence_id integer required
     * @response   {
     *       "status": "success",
     *       "message": "Evendance deleted successfully"
     *   }
    **/

    public function destroy(Eveidence $eveidence)
    {
        $eveidence->delete();

        $data = [
            'status' => 'success', 
            'message'=>'Evendance deleted successfully',             
           
        ];
        
        return  response()->json($data,200);
    }

}

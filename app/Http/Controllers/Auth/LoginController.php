<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class Login<PERSON>ontroller extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Get the post-login redirect path.
     *
     * @return string
     */
    public function redirectTo()
    {
        // Check if user is authenticated and redirect based on user type
        if (auth()->check()) {
            $userType = auth()->user()->user_type;

            switch ($userType) {
                case 'agent':
                    return '/agent_dashboard';
                case 'manager':
                    return '/manager/dashboard';
                default:
                    return $this->redirectTo;
            }
        }

        return $this->redirectTo;
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    function username(){

        return "phone_number";
        
    }
    
}

<?php

namespace App\Http\Controllers;

use App\Models\AgentPosition;
use Illuminate\Http\Request;

class AgentPositionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        foreach ($request->positions as $position_id) {
            AgentPosition::saveAgentPosition($request->agent_id,$position_id);
        }

        return back();
    }

    /**
     * Display the specified resource.
     */
    public function show(AgentPosition $agentPosition)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AgentPosition $agentPosition)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AgentPosition $agentPosition)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($agentPosition)
    {
        AgentPosition::destroy($agentPosition);

        return back();
    }
}

<?php

namespace App\Services;

use App\Models\Vote;
use App\Models\VoteAuditLog;
use App\Models\Agent;
use App\Models\Candidate;
use App\Models\PollingStation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class VoteAuditService
{
    /**
     * Log a vote submission with full audit trail
     */
    public function logVoteSubmission(
        int $agentId,
        int $candidateId,
        int $newVotes,
        string $actionType = 'create',
        string $submissionMethod = 'web',
        ?Request $request = null,
        ?string $notes = null
    ): VoteAuditLog {
        // Get previous vote count
        $previousVote = Vote::where('agent_id', $agentId)
            ->where('candidate_id', $candidateId)
            ->first();
        
        $previousVotes = $previousVote ? $previousVote->number_of_votes : 0;
        $voteDifference = $newVotes - $previousVotes;

        // Get related entities
        $agent = Agent::with(['polling_station', 'user'])->find($agentId);
        $candidate = Candidate::with('position')->find($candidateId);
        
        // Determine who submitted the votes
        $submittedByUserId = Auth::id() ?? $agent->user_id; // Fallback to agent's user
        $submittedByUserType = Auth::user() ? Auth::user()->user_type : $agent->user->user_type;

        // Get request details if available
        $ipAddress = $request ? $request->ip() : null;
        $userAgent = $request ? $request->userAgent() : null;
        $latitude = $request ? $request->input('latitude') : null;
        $longitude = $request ? $request->input('longitude') : null;

        // Create audit log entry
        $auditLog = VoteAuditLog::create([
            'agent_id' => $agentId,
            'candidate_id' => $candidateId,
            'polling_station_id' => $agent->polling_station_id,
            'position_id' => $candidate->position_id,
            'previous_votes' => $previousVotes,
            'new_votes' => $newVotes,
            'vote_difference' => $voteDifference,
            'action_type' => $actionType,
            'submission_method' => $submissionMethod,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'submitted_by_user_id' => $submittedByUserId,
            'submitted_by_user_type' => $submittedByUserType,
            'submission_time' => now(),
            'notes' => $notes
        ]);

        // Auto-flag if suspicious
        $auditLog->autoFlag();

        return $auditLog;
    }

    /**
     * Get audit trail for a specific agent
     */
    public function getAgentAuditTrail(int $agentId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'candidate.position',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('agent_id', $agentId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get audit trail for a specific polling station
     */
    public function getPollingStationAuditTrail(int $pollingStationId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('polling_station_id', $pollingStationId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get audit trail for a specific candidate
     */
    public function getCandidateAuditTrail(int $candidateId, ?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoteAuditLog::with([
            'agent.user',
            'pollingStation',
            'submittedBy',
            'verifiedBy',
            'flaggedBy'
        ])
        ->where('candidate_id', $candidateId)
        ->orderBy('submission_time', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get flagged submissions that need review (paginated)
     */
    public function getFlaggedSubmissions(?int $perPage = 25): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy',
            'flaggedBy'
        ])
        ->flagged()
        ->orderBy('flagged_at', 'desc')
        ->paginate($perPage);
    }

    /**
     * Get flagged submissions as collection (for API)
     */
    public function getFlaggedSubmissionsCollection(?int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy',
            'flaggedBy'
        ])
        ->flagged()
        ->orderBy('flagged_at', 'desc')
        ->limit($limit)
        ->get();
    }

    /**
     * Get submissions that need verification
     */
    public function getUnverifiedSubmissions(?int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy'
        ])
        ->unverified()
        ->orderBy('submission_time', 'desc')
        ->limit($limit)
        ->get();
    }

    /**
     * Get suspicious activity summary
     */
    public function getSuspiciousActivitySummary(): array
    {
        $flaggedCount = VoteAuditLog::flagged()->count();
        $rapidSubmissions = VoteAuditLog::flagged()
            ->where('flag_reason', 'multiple_rapid_submissions')
            ->count();
        $largeChanges = VoteAuditLog::flagged()
            ->where('flag_reason', 'large_vote_change')
            ->count();
        $unusualTiming = VoteAuditLog::flagged()
            ->where('flag_reason', 'unusual_timing')
            ->count();

        $multipleSubmissions = VoteAuditLog::multipleSubmissions()->get();

        return [
            'total_flagged' => $flaggedCount,
            'rapid_submissions' => $rapidSubmissions,
            'large_changes' => $largeChanges,
            'unusual_timing' => $unusualTiming,
            'agents_with_multiple_submissions' => $multipleSubmissions->count(),
            'recent_flagged' => VoteAuditLog::flagged()->recent()->count()
        ];
    }

    /**
     * Verify a submission
     */
    public function verifySubmission(int $auditLogId, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_verified' => true,
            'verified_by_user_id' => Auth::id(),
            'verified_at' => now(),
            'verification_notes' => $notes
        ]);

        return true;
    }

    /**
     * Flag a submission manually
     */
    public function flagSubmission(int $auditLogId, string $reason, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_flagged' => true,
            'flag_reason' => $reason,
            'flag_notes' => $notes,
            'flagged_by_user_id' => Auth::id(),
            'flagged_at' => now()
        ]);

        return true;
    }

    /**
     * Unflag a submission
     */
    public function unflagSubmission(int $auditLogId, ?string $notes = null): bool
    {
        $auditLog = VoteAuditLog::find($auditLogId);
        
        if (!$auditLog) {
            return false;
        }

        $auditLog->update([
            'is_flagged' => false,
            'flag_reason' => null,
            'flag_notes' => $notes,
            'flagged_by_user_id' => null,
            'flagged_at' => null
        ]);

        return true;
    }

    /**
     * Get audit statistics for a time period
     */
    public function getAuditStatistics(Carbon $startDate, Carbon $endDate): array
    {
        $submissions = VoteAuditLog::withinTimeRange($startDate, $endDate);

        return [
            'total_submissions' => $submissions->count(),
            'unique_agents' => $submissions->distinct('agent_id')->count(),
            'unique_stations' => $submissions->distinct('polling_station_id')->count(),
            'flagged_submissions' => $submissions->flagged()->count(),
            'verified_submissions' => $submissions->verified()->count(),
            'api_submissions' => $submissions->where('submission_method', 'api')->count(),
            'web_submissions' => $submissions->where('submission_method', 'web')->count(),
            'manager_submissions' => $submissions->where('submission_method', 'manager_portal')->count(),
            'average_votes_per_submission' => $submissions->avg('new_votes'),
            'total_vote_changes' => $submissions->sum('vote_difference')
        ];
    }

    /**
     * Get enhanced agent activity analytics
     */
    public function getAgentActivityAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        // Get submission patterns by hour
        $submissionsByHour = VoteAuditLog::whereBetween('submission_time', [$startDate, $endDate])
            ->selectRaw('HOUR(submission_time) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Get submission patterns by day
        $submissionsByDay = VoteAuditLog::whereBetween('submission_time', [$startDate, $endDate])
            ->selectRaw('DATE(submission_time) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        // Get most active agents
        $mostActiveAgents = VoteAuditLog::whereBetween('submission_time', [$startDate, $endDate])
            ->with('agent.user')
            ->selectRaw('agent_id, COUNT(*) as submission_count')
            ->groupBy('agent_id')
            ->orderBy('submission_count', 'desc')
            ->limit(10)
            ->get();

        // Get submission methods breakdown
        $submissionMethods = VoteAuditLog::whereBetween('submission_time', [$startDate, $endDate])
            ->selectRaw('submission_method, COUNT(*) as count')
            ->groupBy('submission_method')
            ->get()
            ->pluck('count', 'submission_method')
            ->toArray();

        // Get geographic distribution
        $geographicDistribution = VoteAuditLog::whereBetween('submission_time', [$startDate, $endDate])
            ->with('pollingStation')
            ->selectRaw('polling_station_id, COUNT(*) as count')
            ->groupBy('polling_station_id')
            ->orderBy('count', 'desc')
            ->limit(20)
            ->get();

        return [
            'submissions_by_hour' => $submissionsByHour,
            'submissions_by_day' => $submissionsByDay,
            'most_active_agents' => $mostActiveAgents,
            'submission_methods' => $submissionMethods,
            'geographic_distribution' => $geographicDistribution,
        ];
    }

    /**
     * Get real-time agent activity summary
     */
    public function getRealTimeAgentActivity(): array
    {
        $now = Carbon::now();

        // Last 5 minutes
        $last5Minutes = VoteAuditLog::where('submission_time', '>=', $now->copy()->subMinutes(5))->count();

        // Last hour
        $lastHour = VoteAuditLog::where('submission_time', '>=', $now->copy()->subHour())->count();

        // Today
        $today = VoteAuditLog::whereDate('submission_time', $now->toDateString())->count();

        // Active agents in last hour
        $activeAgents = VoteAuditLog::where('submission_time', '>=', $now->copy()->subHour())
            ->distinct('agent_id')
            ->count();

        // Recent submissions with details
        $recentSubmissions = VoteAuditLog::with(['agent.user', 'candidate', 'pollingStation'])
            ->where('submission_time', '>=', $now->copy()->subMinutes(30))
            ->orderBy('submission_time', 'desc')
            ->limit(10)
            ->get();

        return [
            'last_5_minutes' => $last5Minutes,
            'last_hour' => $lastHour,
            'today' => $today,
            'active_agents_last_hour' => $activeAgents,
            'recent_submissions' => $recentSubmissions,
        ];
    }

    /**
     * Get agent performance metrics
     */
    public function getAgentPerformanceMetrics(int $agentId, Carbon $startDate, Carbon $endDate): array
    {
        $submissions = VoteAuditLog::where('agent_id', $agentId)
            ->whereBetween('submission_time', [$startDate, $endDate])
            ->get();

        $totalSubmissions = $submissions->count();
        $flaggedSubmissions = $submissions->where('is_flagged', true)->count();
        $verifiedSubmissions = $submissions->where('is_verified', true)->count();

        // Calculate submission frequency
        $daysDiff = $startDate->diffInDays($endDate) ?: 1;
        $avgSubmissionsPerDay = round($totalSubmissions / $daysDiff, 2);

        // Get submission times to analyze patterns
        $submissionHours = $submissions->pluck('submission_time')
            ->map(fn($time) => $time->hour)
            ->countBy()
            ->toArray();

        // Calculate reliability score (based on verification rate and flag rate)
        $reliabilityScore = 100;
        if ($totalSubmissions > 0) {
            $flagRate = ($flaggedSubmissions / $totalSubmissions) * 100;
            $verificationRate = ($verifiedSubmissions / $totalSubmissions) * 100;
            $reliabilityScore = max(0, 100 - ($flagRate * 2) + ($verificationRate * 0.5));
        }

        return [
            'total_submissions' => $totalSubmissions,
            'flagged_submissions' => $flaggedSubmissions,
            'verified_submissions' => $verifiedSubmissions,
            'avg_submissions_per_day' => $avgSubmissionsPerDay,
            'submission_hours' => $submissionHours,
            'reliability_score' => round($reliabilityScore, 1),
            'flag_rate' => $totalSubmissions > 0 ? round(($flaggedSubmissions / $totalSubmissions) * 100, 2) : 0,
            'verification_rate' => $totalSubmissions > 0 ? round(($verifiedSubmissions / $totalSubmissions) * 100, 2) : 0,
        ];
    }

    /**
     * Get suspicious activity patterns
     */
    public function getSuspiciousActivityPatterns(): array
    {
        $now = Carbon::now();

        // Agents with multiple rapid submissions
        $rapidSubmitters = VoteAuditLog::selectRaw('agent_id, COUNT(*) as count')
            ->where('submission_time', '>=', $now->subHours(2))
            ->groupBy('agent_id')
            ->having('count', '>', 5)
            ->with('agent.user')
            ->get();

        // Unusual timing submissions (outside 6 AM - 10 PM)
        $unusualTiming = VoteAuditLog::whereRaw('HOUR(submission_time) < 6 OR HOUR(submission_time) > 22')
            ->where('submission_time', '>=', $now->subDays(7))
            ->with(['agent.user', 'candidate', 'pollingStation'])
            ->orderBy('submission_time', 'desc')
            ->limit(20)
            ->get();

        // Large vote changes
        $largeChanges = VoteAuditLog::where('submission_time', '>=', $now->subDays(7))
            ->whereRaw('ABS(vote_difference) > 500')
            ->with(['agent.user', 'candidate', 'pollingStation'])
            ->orderBy('submission_time', 'desc')
            ->limit(20)
            ->get();

        return [
            'rapid_submitters' => $rapidSubmitters,
            'unusual_timing' => $unusualTiming,
            'large_changes' => $largeChanges,
        ];
    }
}

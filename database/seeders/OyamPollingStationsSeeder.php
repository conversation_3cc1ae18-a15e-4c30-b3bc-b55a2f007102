<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PollingStation;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OyamPollingStationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Oyam Polling Stations import from CSV files...');

        // Check for existing Oyam polling stations
        $existingCount = PollingStation::where('district', 'Oyam')->count();
        if ($existingCount > 0) {
            $this->command->info("Found {$existingCount} existing Oyam polling stations.");
            $this->command->info('Skipping deletion due to foreign key constraints.');
            $this->command->info('Will only add new stations that don\'t already exist.');
        }

        // Oyam District boundaries (approximate)
        $oyamBounds = [
            'minLat' => 2.0,
            'maxLat' => 2.6,
            'minLng' => 32.2,
            'maxLng' => 33.0
        ];

        // Sub-county center coordinates (approximate)
        $subcountyCoordinates = [
            'ACHABA' => ['lat' => 2.3, 'lng' => 32.4],
            'ICHEME' => ['lat' => 2.2, 'lng' => 32.5],
            'NGAI' => ['lat' => 2.4, 'lng' => 32.6],
            'OTWAL' => ['lat' => 2.1, 'lng' => 32.3],
            'OYAM TOWN COUNCIL' => ['lat' => 2.25, 'lng' => 32.45],
            'ALEKA' => ['lat' => 2.35, 'lng' => 32.35],
            'ABOK' => ['lat' => 2.15, 'lng' => 32.55],
            'ICHEME TOWN COUNCIL' => ['lat' => 2.2, 'lng' => 32.5],
            'ABER' => ['lat' => 2.05, 'lng' => 32.25],
            'LORO' => ['lat' => 2.0, 'lng' => 32.2],
            'MINAKULU TOWN COUNCIL' => ['lat' => 2.1, 'lng' => 32.4],
            'MINAKULU' => ['lat' => 2.1, 'lng' => 32.4],
        ];

        // CSV file paths
        $csvFiles = [
            public_path('files/polling_station.csv')
        ];

        // Check if CSV files exist
        $csvFile = null;
        foreach ($csvFiles as $file) {
            if (file_exists($file)) {
                $csvFile = $file;
                $this->command->info("Found CSV file: " . basename($file));
                break;
            }
        }

        if (!$csvFile) {
            $this->command->error('No CSV file found in public/files/');
            return;
        }

        // Common Ugandan names for agents
        $firstNames = [
            'James', 'Mary', 'John', 'Sarah', 'David', 'Grace', 'Peter', 'Jane',
            'Paul', 'Rose', 'Moses', 'Ruth', 'Samuel', 'Joyce', 'Daniel', 'Agnes',
            'Joseph', 'Margaret', 'Michael', 'Catherine', 'Robert', 'Betty', 'Francis',
            'Alice', 'Emmanuel', 'Florence', 'Isaac', 'Esther', 'Charles', 'Helen',
            'Patrick', 'Susan', 'Stephen', 'Christine', 'Richard', 'Josephine',
            'Anthony', 'Winnie', 'George', 'Stella', 'Martin', 'Doreen', 'Fred',
            'Irene', 'Simon', 'Lydia', 'Andrew', 'Brenda', 'Kenneth', 'Mercy'
        ];

        $lastNames = [
            'Okello', 'Akello', 'Obwona', 'Aceng', 'Odongo', 'Akot', 'Opio',
            'Aber', 'Ocen', 'Akumu', 'Otim', 'Auma', 'Ogwal', 'Atim', 'Okot',
            'Adong', 'Olanya', 'Awor', 'Ocan', 'Apio', 'Omara', 'Akech', 'Ojok',
            'Anek', 'Olum', 'Awino', 'Ocitti', 'Apiyo', 'Omoding', 'Akello',
            'Onen', 'Aweko', 'Odoch', 'Atoo', 'Olweny', 'Acan', 'Okumu', 'Aber',
            'Oneka', 'Awor', 'Odong', 'Akot', 'Olwoch', 'Achan', 'Okwir', 'Abalo'
        ];

        // Phone number prefixes for Uganda
        $phonePrefix = ['0701', '0702', '0703', '0704', '0705', '0706', '0707', '0708', '0709',
                       '0750', '0751', '0752', '0753', '0754', '0755', '0756', '0757', '0758', '0759',
                       '0770', '0771', '0772', '0773', '0774', '0775', '0776', '0777', '0778', '0779',
                       '0780', '0781', '0782', '0783', '0784', '0785', '0786', '0787', '0788', '0789'];

        DB::beginTransaction();

        try {
            // Read and process CSV file
            $csvData = $this->readCsvFile($csvFile);
            $stationCount = 0;
            $geocodedCount = 0;
            $approximateCount = 0;

            $this->command->info("Processing " . count($csvData) . " polling stations from CSV...");

            foreach ($csvData as $index => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Extract data based on CSV structure
                $stationData = $this->extractStationData($row, $csvFile);

                if (!$stationData) {
                    continue;
                }

                // Get coordinates for the station
                $coordinates = $this->getStationCoordinates(
                    $stationData,
                    $subcountyCoordinates,
                    $oyamBounds
                );

                if ($coordinates['method'] === 'geocoded') {
                    $geocodedCount++;
                } else {
                    $approximateCount++;
                }

                // Check if polling station already exists
                $existingStation = PollingStation::where('name', $stationData['name'])
                    ->where('district', $stationData['district'])
                    ->where('subcounty', $stationData['subcounty'])
                    ->first();

                if ($existingStation) {
                    // Update coordinates if they don't exist
                    if (!$existingStation->latitude || !$existingStation->longitude) {
                        $existingStation->update([
                            'latitude' => $coordinates['latitude'],
                            'longitude' => $coordinates['longitude'],
                        ]);
                        $this->command->info("Updated coordinates for: " . $stationData['name']);
                    }
                    continue; // Skip creating agent for existing station
                }

                // Create new polling station
                $pollingStation = PollingStation::create([
                    'name' => $stationData['name'],
                    'district' => $stationData['district'],
                    'county' => $stationData['county'],
                    'subcounty' => $stationData['subcounty'],
                    'parish' => $stationData['parish'],
                    'village' => $stationData['parish'], // Using parish as village for now
                    'latitude' => $coordinates['latitude'],
                    'longitude' => $coordinates['longitude'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Create user for the agent
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $phoneNumber = $phonePrefix[array_rand($phonePrefix)] . rand(100000, 999999);
                $fullName = $firstName . ' ' . $lastName;

                $user = User::create([
                    'name' => $fullName,
                    'phone_number' => $phoneNumber,
                    'user_type' => 'agent',
                    'password' => Hash::make('password123'), // Default password
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Create agent linked to the user and polling station
                Agent::create([
                    'user_id' => $user->id,
                    'polling_station_id' => $pollingStation->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $stationCount++;

                if ($stationCount % 50 == 0) {
                    $this->command->info("Created {$stationCount} polling stations... (Geocoded: {$geocodedCount}, Approximate: {$approximateCount})");
                }
            }

            DB::commit();
            $this->command->info("Successfully imported {$stationCount} polling stations!");
            $this->command->info("Geocoded coordinates: {$geocodedCount}");
            $this->command->info("Approximate coordinates: {$approximateCount}");

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error creating polling stations: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Read CSV file and return data array
     */
    private function readCsvFile($filePath): array
    {
        $data = [];
        if (($handle = fopen($filePath, "r")) !== FALSE) {
            $header = fgetcsv($handle, 1000, ","); // Read header
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                if (count($row) >= count($header)) {
                    $data[] = array_combine($header, $row);
                }
            }
            fclose($handle);
        }
        return $data;
    }

    /**
     * Extract station data from CSV row
     */
    private function extractStationData($row, $csvFile): ?array
    {
        // Handle different CSV formats
        if (str_contains($csvFile, 'oyam_polling_geocoding_list.csv')) {
            // Format: Polling Station,Parish,Sub-County,Formatted Address
            return [
                'name' => $row['Polling Station'] ?? '',
                'parish' => $row['Parish'] ?? '',
                'subcounty' => $row['Sub-County'] ?? '',
                'district' => 'Oyam',
                'county' => $this->getCounty($row['Sub-County'] ?? ''),
                'formatted_address' => $row['Formatted Address'] ?? ''
            ];
        } else {
            // Format: District,County,Sub-County,Parish,Polling Station
            return [
                'name' => $row['Polling Station'] ?? '',
                'parish' => $row['Parish'] ?? '',
                'subcounty' => $row['Sub-County'] ?? '',
                'district' => $row['District'] ?? 'Oyam',
                'county' => $row['County'] ?? '',
                'formatted_address' => ''
            ];
        }
    }

    /**
     * Get coordinates for a polling station
     */
    private function getStationCoordinates($stationData, $subcountyCoordinates, $oyamBounds): array
    {
        // Try geocoding first if we have a formatted address
        if (!empty($stationData['formatted_address'])) {
            $geocoded = $this->geocodeAddress($stationData['formatted_address']);
            if ($geocoded) {
                return [
                    'latitude' => $geocoded['lat'],
                    'longitude' => $geocoded['lng'],
                    'method' => 'geocoded'
                ];
            }
        }

        // Try geocoding with constructed address
        $address = $this->constructAddress($stationData);
        $geocoded = $this->geocodeAddress($address);
        if ($geocoded) {
            return [
                'latitude' => $geocoded['lat'],
                'longitude' => $geocoded['lng'],
                'method' => 'geocoded'
            ];
        }

        // Fall back to approximate coordinates based on subcounty
        return $this->getApproximateCoordinates($stationData, $subcountyCoordinates, $oyamBounds);
    }

    /**
     * Construct address for geocoding
     */
    private function constructAddress($stationData): string
    {
        $parts = array_filter([
            $stationData['name'],
            $stationData['parish'],
            $stationData['subcounty'],
            $stationData['district'],
            'Uganda'
        ]);

        return implode(', ', $parts);
    }

    /**
     * Generate random float with specified decimal places
     */
    private function randomFloat($min, $max, $decimals = 6): float
    {
        $scale = pow(10, $decimals);
        return mt_rand($min * $scale, $max * $scale) / $scale;
    }

    /**
     * Geocode an address using OpenStreetMap Nominatim
     */
    private function geocodeAddress($address): ?array
    {
        try {
            $this->command->info("Geocoding: " . substr($address, 0, 50) . "...");

            $response = Http::timeout(10)->get('https://nominatim.openstreetmap.org/search', [
                'q' => $address,
                'format' => 'json',
                'limit' => 1,
                'countrycodes' => 'ug', // Uganda only
                'addressdetails' => 1
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (!empty($data)) {
                    $result = $data[0];
                    $lat = (float) $result['lat'];
                    $lng = (float) $result['lon'];

                    // Validate coordinates are within Uganda
                    if ($this->isWithinUganda($lat, $lng)) {
                        sleep(1); // Be respectful to the API
                        return ['lat' => $lat, 'lng' => $lng];
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning("Geocoding failed for address: {$address}. Error: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Get approximate coordinates based on subcounty
     */
    private function getApproximateCoordinates($stationData, $subcountyCoordinates, $oyamBounds): array
    {
        $subcounty = strtoupper($stationData['subcounty']);

        if (isset($subcountyCoordinates[$subcounty])) {
            $center = $subcountyCoordinates[$subcounty];
            // Add small random offset (within ~2km radius)
            $latOffset = $this->randomFloat(-0.02, 0.02, 6);
            $lngOffset = $this->randomFloat(-0.02, 0.02, 6);

            return [
                'latitude' => $center['lat'] + $latOffset,
                'longitude' => $center['lng'] + $lngOffset,
                'method' => 'approximate'
            ];
        }

        // Ultimate fallback - random within Oyam bounds
        return [
            'latitude' => $this->randomFloat($oyamBounds['minLat'], $oyamBounds['maxLat'], 6),
            'longitude' => $this->randomFloat($oyamBounds['minLng'], $oyamBounds['maxLng'], 6),
            'method' => 'random'
        ];
    }

    /**
     * Check if coordinates are within Uganda
     */
    private function isWithinUganda($lat, $lng): bool
    {
        return $lat >= -1.5 && $lat <= 4.3 && $lng >= 29.5 && $lng <= 35.0;
    }

    /**
     * Get county for subcounty
     */
    private function getCounty($subcounty): string
    {
        $counties = [
            'ACHABA' => 'Oyam North',
            'ICHEME' => 'Oyam North',
            'NGAI' => 'Oyam North',
            'OTWAL' => 'Oyam North',
            'OYAM TOWN COUNCIL' => 'Oyam North',
            'ALEKA' => 'Oyam North',
            'ABOK' => 'Oyam North',
            'ICHEME TOWN COUNCIL' => 'Oyam North',
            'ABER' => 'Oyam South',
            'LORO' => 'Oyam South',
            'MINAKULU TOWN COUNCIL' => 'Oyam South',
            'MINAKULU' => 'Oyam South',
        ];

        return $counties[strtoupper($subcounty)] ?? 'Oyam Central';
    }
}

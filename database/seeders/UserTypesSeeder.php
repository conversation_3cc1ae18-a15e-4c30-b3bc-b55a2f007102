<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * This seeder creates all user types: admin, manager, and viewer users
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating user accounts for all user types...');
        $this->command->newLine();

        // Ensure roles exist first
        $this->call(RoleSeeder::class);
        
        // Create admin users
        $this->command->info('👑 Creating admin users...');
        $this->call(AdminUserSeeder::class);
        $this->command->newLine();

        // Create polling managers
        $this->command->info('📊 Creating polling manager users...');
        $this->call(PollingManagerSeeder::class);
        $this->command->newLine();

        // Create dashboard viewers
        $this->command->info('👀 Creating dashboard viewer users...');
        $this->call(DashboardViewerSeeder::class);
        $this->command->newLine();

        $this->command->info('✅ All user types created successfully!');
        $this->command->newLine();
        
        // Display summary
        $this->displayUserSummary();
    }

    /**
     * Display a summary of created users
     */
    private function displayUserSummary(): void
    {
        $this->command->info('📋 USER ACCOUNTS SUMMARY:');
        $this->command->info('═══════════════════════════════════════════════════════════════');
        
        $this->command->info('🔐 ADMIN USERS:');
        $this->command->info('  • System Administrator: +************ (password: admin123)');
        $this->command->newLine();
        
        $this->command->info('📊 POLLING MANAGERS:');
        $this->command->info('  • Polling Station Manager: +************ (password: manager123)');
        $this->command->info('  • District Manager - Oyam: +************ (password: manager123)');
        $this->command->info('  • County Manager - Aber: +************ (password: manager123)');
        $this->command->info('  • Regional Manager - Northern: +************ (password: manager123)');
        $this->command->newLine();
        
        $this->command->info('👀 DASHBOARD VIEWERS:');
        $this->command->info('  • Dashboard Viewer: +256700000002 (password: viewer123)');
        $this->command->info('  • Election Observer - EU: +256700000021 (password: viewer123)');
        $this->command->info('  • Media Representative - NTV: +256700000022 (password: viewer123)');
        $this->command->info('  • Party Agent - NRM: +256700000023 (password: viewer123)');
        $this->command->info('  • Party Agent - NUP: +256700000024 (password: viewer123)');
        $this->command->info('  • Civil Society Observer: +256700000025 (password: viewer123)');
        $this->command->info('  • Government Official: +256700000026 (password: viewer123)');
        $this->command->newLine();
        
        $this->command->warn('⚠️  SECURITY NOTICE:');
        $this->command->warn('   Please change all default passwords after first login!');
        $this->command->newLine();
        
        $this->command->info('🌐 ACCESS URLS:');
        $this->command->info('  • Admin Dashboard: /home');
        $this->command->info('  • Manager Dashboard: /manager/dashboard');
        $this->command->info('  • Viewer Dashboard: /home (read-only)');
        $this->command->info('═══════════════════════════════════════════════════════════════');
    }
}

<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class PollingManagerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the polling station manager role
        $managerRole = Role::where('name', 'polling_station_manager')->first();

        if (!$managerRole) {
            $this->command->error('Polling station manager role not found. Please run RoleSeeder first.');
            return;
        }

        // Create polling manager user
        $managerUser = User::updateOrCreate(
            ['phone_number' => '+256700000001'],
            [
                'name' => 'Polling Station Manager',
                'phone_number' => '+256700000001',
                'password' => Hash::make('manager123'),
                'user_type' => 'manager',
                'role_id' => $managerRole->id,
                'is_active' => true,
                'email_verified_at' => now()
            ]
        );

        // Assign manager role to user (both primary and additional)
        $managerUser->roles()->syncWithoutDetaching([$managerRole->id]);

        $this->command->info('Polling manager user created successfully!');
        $this->command->info('Phone: +256700000001');
        $this->command->info('Password: manager123');
        $this->command->warn('Please change the default password after first login.');

        // Create additional polling managers for testing
        $additionalManagers = [
            [
                'name' => 'District Manager - Oyam',
                'phone_number' => '+256700000011',
                'password' => 'manager123'
            ],
            [
                'name' => 'County Manager - Aber',
                'phone_number' => '+256700000012',
                'password' => 'manager123'
            ],
            [
                'name' => 'Regional Manager - Northern',
                'phone_number' => '+256700000013',
                'password' => 'manager123'
            ]
        ];

        foreach ($additionalManagers as $managerData) {
            $manager = User::updateOrCreate(
                ['phone_number' => $managerData['phone_number']],
                [
                    'name' => $managerData['name'],
                    'phone_number' => $managerData['phone_number'],
                    'password' => Hash::make($managerData['password']),
                    'user_type' => 'manager',
                    'role_id' => $managerRole->id,
                    'is_active' => true,
                    'email_verified_at' => now()
                ]
            );

            $manager->roles()->syncWithoutDetaching([$managerRole->id]);
            
            $this->command->info("Created manager: {$managerData['name']} ({$managerData['phone_number']})");
        }
    }
}

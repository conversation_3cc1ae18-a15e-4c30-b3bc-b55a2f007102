<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DashboardViewerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the dashboard viewer role
        $viewerRole = Role::where('name', 'dashboard_viewer')->first();

        if (!$viewerRole) {
            $this->command->error('Dashboard viewer role not found. Please run RoleSeeder first.');
            return;
        }

        // Create dashboard viewer user
        $viewerUser = User::updateOrCreate(
            ['phone_number' => '+256700000002'],
            [
                'name' => 'Dashboard Viewer',
                'phone_number' => '+256700000002',
                'password' => Hash::make('viewer123'),
                'user_type' => 'viewer',
                'role_id' => $viewerRole->id,
                'is_active' => true,
                'email_verified_at' => now()
            ]
        );

        // Assign viewer role to user (both primary and additional)
        $viewerUser->roles()->syncWithoutDetaching([$viewerRole->id]);

        $this->command->info('Dashboard viewer user created successfully!');
        $this->command->info('Phone: +256700000002');
        $this->command->info('Password: viewer123');
        $this->command->warn('Please change the default password after first login.');

        // Create additional viewers for different stakeholders
        $additionalViewers = [
            [
                'name' => 'Election Observer - EU',
                'phone_number' => '+256700000021',
                'password' => 'viewer123'
            ],
            [
                'name' => 'Media Representative - NTV',
                'phone_number' => '+256700000022',
                'password' => 'viewer123'
            ],
            [
                'name' => 'Party Agent - NRM',
                'phone_number' => '+256700000023',
                'password' => 'viewer123'
            ],
            [
                'name' => 'Party Agent - NUP',
                'phone_number' => '+256700000024',
                'password' => 'viewer123'
            ],
            [
                'name' => 'Civil Society Observer',
                'phone_number' => '+256700000025',
                'password' => 'viewer123'
            ],
            [
                'name' => 'Government Official',
                'phone_number' => '+256700000026',
                'password' => 'viewer123'
            ]
        ];

        foreach ($additionalViewers as $viewerData) {
            $viewer = User::updateOrCreate(
                ['phone_number' => $viewerData['phone_number']],
                [
                    'name' => $viewerData['name'],
                    'phone_number' => $viewerData['phone_number'],
                    'password' => Hash::make($viewerData['password']),
                    'user_type' => 'viewer',
                    'role_id' => $viewerRole->id,
                    'is_active' => true,
                    'email_verified_at' => now()
                ]
            );

            $viewer->roles()->syncWithoutDetaching([$viewerRole->id]);
            
            $this->command->info("Created viewer: {$viewerData['name']} ({$viewerData['phone_number']})");
        }
    }
}

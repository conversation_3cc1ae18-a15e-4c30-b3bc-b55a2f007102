<?php

namespace Database\Seeders;

use App\Models\Agent;
use App\Models\AgentPosition;
use App\Models\Position;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateAgentsToPositionOneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to update all agents to use position ID 1...');

        // Check if position ID 1 exists
        $position = Position::find(1);
        if (!$position) {
            $this->command->error('Position with ID 1 does not exist. Please create it first.');
            return;
        }

        $this->command->info("Position found: {$position->name}");

        // Get all agents
        $agents = Agent::all();
        $totalAgents = $agents->count();

        if ($totalAgents === 0) {
            $this->command->info('No agents found in the database.');
            return;
        }

        $this->command->info("Found {$totalAgents} agents to update...");

        DB::beginTransaction();

        try {
            $updatedCount = 0;

            foreach ($agents as $agent) {
                // Delete existing agent positions for this agent
                AgentPosition::where('agent_id', $agent->id)->delete();

                // Create new agent position with position ID 1
                AgentPosition::saveAgentPosition($agent->id, 1);

                $updatedCount++;

                // Show progress every 50 agents
                if ($updatedCount % 50 === 0) {
                    $this->command->info("Updated {$updatedCount}/{$totalAgents} agents...");
                }
            }

            DB::commit();

            $this->command->info("Successfully updated all {$updatedCount} agents to use position ID 1 ({$position->name})!");

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error updating agents: ' . $e->getMessage());
            throw $e;
        }
    }
}

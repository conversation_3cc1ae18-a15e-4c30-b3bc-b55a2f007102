# Database Seeders

This directory contains seeders for creating different types of users in the vote counting system.

## Available Seeders

### 1. UserTypesSeeder
**Recommended for development setup**

Creates all user types with sample accounts:

```bash
php artisan db:seed --class=UserTypesSeeder
```

This will create:
- 1 Admin user
- 4 Polling Manager users  
- 7 Dashboard Viewer users

### 2. Individual Seeders

#### AdminUserSeeder
Creates system administrator accounts:

```bash
php artisan db:seed --class=AdminUserSeeder
```

#### PollingManagerSeeder
Creates polling station manager accounts:

```bash
php artisan db:seed --class=PollingManagerSeeder
```

#### DashboardViewerSeeder
Creates dashboard viewer accounts:

```bash
php artisan db:seed --class=DashboardViewerSeeder
```

## Created User Accounts

### 🔐 Admin Users
| Name | Phone | Password | Access |
|------|-------|----------|---------|
| System Administrator | +************ | admin123 | Full system access |

### 📊 Polling Managers
| Name | Phone | Password | Access |
|------|-------|----------|---------|
| Polling Station Manager | +************ | manager123 | Manager dashboard |
| District Manager - Oyam | +************ | manager123 | Manager dashboard |
| County Manager - Aber | +************ | manager123 | Manager dashboard |
| Regional Manager - Northern | +************ | manager123 | Manager dashboard |

### 👀 Dashboard Viewers
| Name | Phone | Password | Access |
|------|-------|----------|---------|
| Dashboard Viewer | +************ | viewer123 | Read-only dashboard |
| Election Observer - EU | +************ | viewer123 | Read-only dashboard |
| Media Representative - NTV | +************ | viewer123 | Read-only dashboard |
| Party Agent - NRM | +************ | viewer123 | Read-only dashboard |
| Party Agent - NUP | +************ | viewer123 | Read-only dashboard |
| Civil Society Observer | +************ | viewer123 | Read-only dashboard |
| Government Official | +************ | viewer123 | Read-only dashboard |

## Access URLs

- **Admin Dashboard**: `/home` (full access)
- **Manager Dashboard**: `/manager/dashboard` (vote submission & evidence upload)
- **Viewer Dashboard**: `/home` (read-only access)

## Security Notes

⚠️ **Important**: All accounts use default passwords. Change them immediately after first login!

## Prerequisites

Before running user seeders, ensure:

1. Database is migrated:
   ```bash
   php artisan migrate
   ```

2. Roles are seeded:
   ```bash
   php artisan db:seed --class=RoleSeeder
   ```

## Usage Examples

### Development Setup
```bash
# Fresh start
php artisan migrate:fresh
php artisan db:seed --class=UserTypesSeeder
```

### Production Setup
```bash
# Only create admin user
php artisan db:seed --class=RoleSeeder
php artisan db:seed --class=AdminUserSeeder
```

### Add More Managers
```bash
php artisan db:seed --class=PollingManagerSeeder
```

## Testing

Run tests to verify seeders work correctly:

```bash
php artisan test tests/Feature/SeededUsersTest.php
```

## Customization

To modify user accounts, edit the respective seeder files:
- `AdminUserSeeder.php` - Admin accounts
- `PollingManagerSeeder.php` - Manager accounts  
- `DashboardViewerSeeder.php` - Viewer accounts

## User Roles & Permissions

Each user type automatically gets appropriate roles:
- **Admin**: `admin` role (all permissions)
- **Manager**: `polling_station_manager` role (vote management)
- **Viewer**: `dashboard_viewer` role (read-only access)

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vote_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agent_id')->constrained()->onDelete('cascade');
            $table->foreignId('candidate_id')->constrained()->onDelete('cascade');
            $table->foreignId('polling_station_id')->constrained()->onDelete('cascade');
            $table->foreignId('position_id')->constrained()->onDelete('cascade');
            
            // Vote data
            $table->integer('previous_votes')->nullable()->comment('Previous vote count before this submission');
            $table->integer('new_votes')->comment('New vote count in this submission');
            $table->integer('vote_difference')->comment('Difference between new and previous votes');
            
            // Submission details
            $table->enum('action_type', ['create', 'update', 'delete'])->default('create');
            $table->enum('submission_method', ['web', 'api', 'manager_portal'])->default('web');
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            
            // Location data
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            
            // Audit metadata
            $table->foreignId('submitted_by_user_id')->constrained('users')->onDelete('cascade');
            $table->enum('submitted_by_user_type', ['agent', 'manager', 'admin']);
            $table->timestamp('submission_time');
            $table->text('notes')->nullable()->comment('Additional notes or remarks');
            
            // Verification status
            $table->boolean('is_verified')->default(false);
            $table->foreignId('verified_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('verified_at')->nullable();
            $table->text('verification_notes')->nullable();
            
            // Flags for suspicious activity
            $table->boolean('is_flagged')->default(false);
            $table->enum('flag_reason', ['multiple_rapid_submissions', 'large_vote_change', 'unusual_timing', 'manual_flag', 'other'])->nullable();
            $table->text('flag_notes')->nullable();
            $table->foreignId('flagged_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('flagged_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['agent_id', 'candidate_id']);
            $table->index(['polling_station_id', 'submission_time']);
            $table->index(['submitted_by_user_id', 'submission_time']);
            $table->index(['is_flagged', 'submission_time']);
            $table->index('submission_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vote_audit_logs');
    }
};

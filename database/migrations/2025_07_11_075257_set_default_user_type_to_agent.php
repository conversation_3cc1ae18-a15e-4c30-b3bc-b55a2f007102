<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Set default value for user_type column to 'agent'
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent', 'manager', 'viewer') NOT NULL DEFAULT 'agent'");

        // Update existing users with null user_type to 'agent'
        DB::statement("UPDATE users SET user_type = 'agent' WHERE user_type IS NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove default value
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent', 'manager', 'viewer') NOT NULL");
    }
};

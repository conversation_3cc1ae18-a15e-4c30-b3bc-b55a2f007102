    :root {
            /* Gold/Yellow Gradient Color Scheme */
            --primary-color: #FFA500;
            --primary-light: #FFD700;
            --primary-dark: #FF8C00;
            --secondary-gold: #DAA520;
            --accent-yellow: #FFEB3B;
            --gold-gradient: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
            --gold-gradient-hover: linear-gradient(135deg, #FF8C00 0%, #FFA500 100%);
            --gold-gradient-light: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(255, 215, 0, 0.1) 100%);

            /* Text Colors */
            --text-dark: #2c3e50;
            --text-medium: #495057;
            --text-light: #6c757d;
            --text-muted: #8e9ba7;

            /* Background Colors */
            --white: #ffffff;
            --light-bg: #f8f9fa;
            --card-bg: #ffffff;
            --border-color: #e9ecef;
            --hover-bg: rgba(255, 165, 0, 0.05);

            /* Design System Variables */
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-sm: 6px;
            --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            --box-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.1);
            --box-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;

            /* Typography */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: var(--text-dark);
        }
        
        /* Navbar Styling - Compact Version */
        .navbar-custom {
            background: #fff;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
            padding: 0.4rem 1rem;
            min-height: 50px;
        }

        .navbar-brand-custom {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.2rem;
            color: var(--primary-color) !important;
        }

        .navbar-brand-custom .brand-icon {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            box-shadow: 0 2px 6px rgba(255, 165, 0, 0.15);
            font-size: 0.9rem;
        }
        
        .nav-link-custom {
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            margin: 0 2px;
            position: relative;
            font-size: 0.9rem;
        }

        .nav-link-custom:hover, .nav-link-custom.active {
            color: var(--primary-color);
            background-color: rgba(255, 165, 0, 0.08);
        }

        .nav-link-custom.active::after {
            content: '';
            position: absolute;
            bottom: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 2px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 6px;
        }

        .nav-link-custom i {
            margin-right: 5px;
            font-size: 0.85rem;
        }
        
        .navbar-toggler-custom {
            border: none;
            padding: 0;
        }
        
        .navbar-toggler-custom:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon-custom {
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: relative;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before,
        .navbar-toggler-icon-custom::after {
            content: '';
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: absolute;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before {
            top: -6px;
        }
        
        .navbar-toggler-icon-custom::after {
            bottom: -6px;
        }
        
        /* User dropdown - Compact */
        .user-dropdown {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.08) 0%, rgba(255, 215, 0, 0.08) 100%);
            border-radius: 20px;
            padding: 3px 10px 3px 3px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .user-dropdown:hover {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.12) 0%, rgba(255, 215, 0, 0.12) 100%);
        }

        .user-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 6px;
            font-size: 0.8rem;
        }

        .user-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 4px;
            font-size: 0.85rem;
        }
        
        .dropdown-menu-custom {
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            padding: 0.4rem 0;
            min-width: 180px;
            margin-top: 6px;
        }

        .dropdown-item-custom {
            padding: 0.5rem 1rem;
            font-weight: 500;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            transition: all 0.2s;
            font-size: 0.85rem;
        }

        .dropdown-item-custom i {
            margin-right: 8px;
            font-size: 0.9rem;
            width: 16px;
        }

        .dropdown-item-custom:hover {
            background-color: rgba(255, 165, 0, 0.08);
            color: var(--primary-color);
        }
        
        .dropdown-item-custom.logout {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: #dc3545;
        }
        
        .dropdown-item-custom.logout:hover {
            background-color: rgba(220, 53, 69, 0.05);
        }
        
        /* Main content area - Compact */
        .main-content {
            padding: 1.5rem 0;
            width: 100%;
            max-width: none;
        }

        /* Ensure full width utilization */
        .container-fluid {
            padding-left: 1rem;
            padding-right: 1rem;
            max-width: none;
            width: 100%;
        }

        /* Remove any width constraints on common elements */
        .row {
            margin-left: 0;
            margin-right: 0;
        }

        .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
        .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-auto, .col-sm, .col-md, .col-lg, .col-xl, .col-xxl {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }
        
        /* ===== ALERT SYSTEM ===== */
        .alert-custom {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
            padding: var(--spacing-md) var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .alert-gold {
            background: var(--gold-gradient-light);
            border-left: 4px solid var(--primary-color);
            color: var(--text-dark);
        }

        .alert-success-custom {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
            border-left: 4px solid #28a745;
            color: var(--text-dark);
        }

        .alert-danger-custom {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(176, 42, 55, 0.1) 100%);
            border-left: 4px solid #dc3545;
            color: var(--text-dark);
        }

        .alert-warning-custom {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
            border-left: 4px solid #ffc107;
            color: var(--text-dark);
        }

        /* ===== MODAL SYSTEM ===== */
        .modal-content-custom {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-lg);
            overflow: hidden;
        }

        .modal-header-gold {
            background: var(--gold-gradient);
            color: white;
            border-bottom: none;
            padding: var(--spacing-lg);
        }

        .modal-header-gold .btn-close {
            filter: brightness(0) invert(1);
        }

        .modal-body-custom {
            padding: var(--spacing-lg);
        }

        .modal-footer-custom {
            background: var(--light-bg);
            border-top: 1px solid var(--border-color);
            padding: var(--spacing-md) var(--spacing-lg);
        }

        /* ===== TOAST SYSTEM ===== */
        .toast-custom {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-hover);
            backdrop-filter: blur(10px);
        }

        .toast-header-gold {
            background: var(--gold-gradient-light);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
        }

        .toast-body-custom {
            padding: var(--spacing-md);
            background: var(--white);
        }

        /* ===== PAGINATION SYSTEM ===== */
        .pagination-custom .page-link {
            border: 1px solid var(--border-color);
            color: var(--text-medium);
            padding: 0.5rem 0.75rem;
            margin: 0 2px;
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
        }

        .pagination-custom .page-link:hover {
            background: var(--gold-gradient-light);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .pagination-custom .page-item.active .page-link {
            background: var(--gold-gradient);
            border-color: var(--primary-color);
            color: white;
        }

        /* ===== DROPDOWN SYSTEM ===== */
        .dropdown-menu-custom {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-hover);
            padding: var(--spacing-sm) 0;
            margin-top: var(--spacing-xs);
        }

        .dropdown-item-custom {
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-medium);
            font-weight: var(--font-weight-medium);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }

        .dropdown-item-custom:hover {
            background: var(--gold-gradient-light);
            color: var(--primary-color);
        }

        .dropdown-item-custom i {
            margin-right: var(--spacing-sm);
            width: 16px;
            text-align: center;
        }
        
        /* Compact navbar additions */
        .navbar-nav .nav-item {
            margin: 0 1px;
        }

        .navbar-nav .dropdown-toggle::after {
            font-size: 0.7rem;
            margin-left: 0.3rem;
        }

        /* Notification bell styling */
        #notificationDropdown {
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        #notificationDropdown:hover {
            background-color: rgba(255, 165, 0, 0.08);
        }

        /* ===== BUTTON SYSTEM ===== */
        .btn-gold {
            background: var(--gold-gradient);
            border: none;
            color: white;
            font-weight: var(--font-weight-medium);
            padding: 0.5rem 1.25rem;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(255, 165, 0, 0.2);
        }

        .btn-gold:hover {
            background: var(--gold-gradient-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
            color: white;
        }

        .btn-gold:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(255, 165, 0, 0.2);
        }

        .btn-gold-outline {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: var(--font-weight-medium);
            padding: 0.4rem 1.25rem;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }

        .btn-gold-outline:hover {
            background: var(--gold-gradient);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 165, 0, 0.2);
        }

        .btn-compact {
            padding: 0.375rem 0.75rem;
            font-size: var(--font-size-sm);
            border-radius: var(--border-radius-sm);
        }

        .btn-lg-custom {
            padding: 0.75rem 2rem;
            font-size: var(--font-size-lg);
            border-radius: var(--border-radius-lg);
        }

        /* ===== CARD SYSTEM ===== */
        .card-custom {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow);
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .card-custom:hover {
            box-shadow: var(--box-shadow-hover);
            transform: translateY(-2px);
        }

        .card-header-gold {
            background: var(--gold-gradient);
            color: white;
            padding: var(--spacing-lg);
            border-bottom: none;
            font-weight: var(--font-weight-semibold);
        }

        .card-header-light {
            background: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-md) var(--spacing-lg);
            font-weight: var(--font-weight-medium);
            color: var(--text-dark);
        }

        .card-body-compact {
            padding: var(--spacing-md) var(--spacing-lg);
        }

        /* ===== FORM SYSTEM ===== */
        .form-control-custom {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.625rem 0.875rem;
            font-size: var(--font-size-base);
            transition: all 0.2s ease;
            background-color: var(--white);
        }

        .form-control-custom:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(255, 165, 0, 0.15);
            outline: none;
        }

        .form-label-custom {
            font-weight: var(--font-weight-medium);
            color: var(--text-dark);
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }

        .form-group-compact {
            margin-bottom: var(--spacing-md);
        }

        .input-group-gold .input-group-text {
            background: var(--gold-gradient-light);
            border: 2px solid var(--border-color);
            border-right: none;
            color: var(--primary-color);
        }

        /* ===== TABLE SYSTEM ===== */
        .table-custom {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .table-custom thead th {
            background: var(--gold-gradient-light);
            border: none;
            font-weight: var(--font-weight-semibold);
            color: var(--text-dark);
            padding: var(--spacing-md);
            font-size: var(--font-size-sm);
        }

        .table-custom tbody td {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table-custom tbody tr:hover {
            background-color: var(--hover-bg);
        }

        .table-custom tbody tr:last-child td {
            border-bottom: none;
        }

        /* ===== BADGE SYSTEM ===== */
        .badge-gold {
            background: var(--gold-gradient);
            color: white;
            font-weight: var(--font-weight-medium);
            padding: 0.375rem 0.75rem;
            border-radius: var(--border-radius);
        }

        .badge-outline {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: var(--font-weight-medium);
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
        }

        /* Wide screen optimizations */
        @media (min-width: 1400px) {
            .container-fluid {
                padding-left: 2rem;
                padding-right: 2rem;
            }

            .main-content {
                padding: 2rem 0;
            }

            /* Optimize card layouts for wider screens */
            .col-lg-4 {
                flex: 0 0 auto;
                width: 20%; /* 5 cards per row instead of 3 */
            }

            /* Optimize table layouts */
            .table-responsive {
                font-size: 0.95rem;
            }

            /* Better spacing for wide screens */
            .card {
                margin-bottom: 1.5rem;
            }
        }

        @media (min-width: 1920px) {
            .container-fluid {
                padding-left: 3rem;
                padding-right: 3rem;
            }

            /* Even more cards per row on very wide screens */
            .col-lg-4 {
                width: 16.666667%; /* 6 cards per row */
            }

            /* Larger font sizes for better readability on large screens */
            body {
                font-size: 1.05rem;
            }

            .table {
                font-size: 1rem;
            }
        }

        /* ===== UTILITY CLASSES ===== */
        .text-gold {
            color: var(--primary-color) !important;
        }

        .bg-gold {
            background: var(--gold-gradient) !important;
        }

        .border-gold {
            border-color: var(--primary-color) !important;
        }

        .shadow-custom {
            box-shadow: var(--box-shadow) !important;
        }

        .shadow-hover {
            box-shadow: var(--box-shadow-hover) !important;
        }

        .rounded-custom {
            border-radius: var(--border-radius) !important;
        }

        .rounded-lg-custom {
            border-radius: var(--border-radius-lg) !important;
        }

        .compact-spacing {
            padding: var(--spacing-sm) var(--spacing-md) !important;
        }

        .hover-lift {
            transition: transform 0.2s ease;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 992px) {
            .navbar-collapse {
                background-color: white;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow-hover);
                padding: var(--spacing-md);
                margin-top: var(--spacing-md);
            }

            .nav-link-custom.active::after {
                display: none;
            }

            .user-dropdown {
                margin-top: var(--spacing-md);
                justify-content: center;
            }

            .navbar-custom {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .card-custom {
                margin-bottom: var(--spacing-md);
            }

            .btn-lg-custom {
                padding: var(--spacing-sm) var(--spacing-lg);
                font-size: var(--font-size-base);
            }
        }

        @media (max-width: 768px) {
            .navbar-brand-custom {
                font-size: var(--font-size-lg);
            }

            .navbar-brand-custom .brand-icon {
                width: 28px;
                height: 28px;
                font-size: var(--font-size-sm);
            }

            .user-name {
                display: none !important;
            }

            .table-custom {
                font-size: var(--font-size-sm);
            }

            .card-body-compact {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .modal-body-custom {
                padding: var(--spacing-md);
            }

            .btn-compact {
                padding: 0.25rem 0.5rem;
                font-size: var(--font-size-xs);
            }
        }

        @media (max-width: 576px) {
            .container-fluid {
                padding-left: var(--spacing-sm);
                padding-right: var(--spacing-sm);
            }

            .card-header-gold,
            .card-body-compact {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .table-custom thead th,
            .table-custom tbody td {
                padding: var(--spacing-sm);
                font-size: var(--font-size-xs);
            }

            .btn-gold,
            .btn-gold-outline {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: var(--font-size-sm);
            }
        }
/* Mobile-friendly styles for agent dashboard */
@media (max-width: 767px) {
    /* General container adjustments */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Button adjustments */
    .btn-sm {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    /* Station card adjustments */
    .station-header {
        padding: 0.75rem 1rem;
        flex-direction: column;
    }
    
    .station-name {
        font-size: 1rem;
        width: 100%;
    }
    
    .station-body {
        padding: 1rem;
    }
    
    /* Agent info adjustments */
    .agent-info {
        flex-direction: column;
        align-items: flex-start;
        padding: 0.5rem;
    }
    
    .agent-icon {
        margin-bottom: 0.5rem;
    }
    
    /* Table adjustments */
    .table-responsive {
        margin-bottom: 1rem;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table th, 
    .table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }
    
    /* Evidence list adjustments */
    .evidence-list {
        justify-content: space-between;
    }
    
    .evidence-item {
        width: 100%;
        margin-right: 0;
    }
    
    /* Modal adjustments */
    .modal-body-custom {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .input-group-text {
        padding: 0.4rem 0.6rem;
    }
    
    /* Hide button text on very small screens and just show icons */
    @media (max-width: 400px) {
        .button-text {
            display: none;
        }
        
        .btn-sm {
            padding: 0.4rem;
        }
    }
}

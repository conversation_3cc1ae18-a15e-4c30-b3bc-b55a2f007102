# Vote Count System

A comprehensive election management and vote counting system built with <PERSON><PERSON>, featuring real-time monitoring, evidence management, and role-based access control.

## 🚀 Features

### 🗳️ **Vote Management**
- Real-time vote submission and tracking
- Candidate and position management
- Polling station organization
- Vote validation and audit trails

### 👥 **User Management**
- Role-based access control (Admin, Agent, Viewer)
- User authentication and authorization
- Permission management system
- User activity tracking

### 📊 **Monitoring & Analytics**
- Real-time vote monitoring dashboard
- Candidate performance analytics
- Polling station status tracking
- Evidence management system

### 🏛️ **Administrative Tools**
- Comprehensive admin dashboard
- User and role management
- System audit and logging
- Data export capabilities

## 🛠️ Installation

### Prerequisites
- PHP 8.1 or higher
- Composer
- Node.js and NPM
- MySQL or PostgreSQL database

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vote-count-system
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Build assets**
   ```bash
   npm run build
   ```

6. **Start the application**
   ```bash
   php artisan serve
   ```

## 👤 Default User Accounts

### Administrator
- **Phone**: +************
- **Password**: admin123
- **Access**: Full system administration

### Dashboard Viewer
- **Phone**: +************
- **Password**: viewer123
- **Access**: Read-only dashboard access

> ⚠️ **Important**: Change default passwords after first login for security.

## 🏗️ System Architecture

### User Roles
- **Admin**: Full system access and management
- **Agent**: Vote submission and evidence upload
- **Viewer**: Dashboard viewing and monitoring

### Key Components
- **Polling Stations**: Geographic voting locations
- **Candidates**: Election participants by position
- **Votes**: Vote counts per candidate per station
- **Evidence**: Supporting documentation and images
- **Audit System**: Activity tracking and verification

## 📱 User Interface

### Design System
- **Color Scheme**: Gold and yellow gradient theme
- **Layout**: Compact, space-efficient design
- **Responsive**: Mobile-friendly interface
- **Accessibility**: WCAG compliant design

### Key Features
- Real-time data updates
- Interactive charts and graphs
- Evidence download system
- Advanced filtering and search

## 🔒 Security Features

- Role-based access control
- Secure authentication system
- Activity logging and audit trails
- Data validation and sanitization
- File upload security

## 📈 Monitoring Capabilities

- Real-time vote tracking
- Candidate performance analysis
- Polling station status monitoring
- Evidence management
- System activity auditing

## 🛡️ Testing

Run the test suite:
```bash
php artisan test
```

## 📚 Documentation

Additional documentation available in the `/docs` directory:
- Polling Station Enhancements
- Smooth Refresh System
- API Documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

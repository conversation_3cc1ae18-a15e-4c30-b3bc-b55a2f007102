@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div>
            <h1 class="dashboard-title">Notifications</h1>
            <p class="dashboard-subtitle">View and manage your notifications</p>
        </div>
        
        <div class="d-flex align-items-center">
            @if(Auth::user()->unreadNotifications->count() > 0)
            <form action="{{ route('notifications.read-all') }}" method="POST" class="me-2">
                @csrf
                <button type="submit" class="btn btn-outline-primary">
                    <i class="bi bi-check-all"></i> Mark All as Read
                </button>
            </form>
            @endif
            <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    @if($notifications->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($notifications as $notification)
                                @php
                                    $data = $notification->data;
                                    $isUnread = $notification->read_at === null;
                                    $isVoteGapAlert = isset($data['candidate_id']);
                                @endphp
                                
                                <div class="list-group-item {{ $isUnread ? 'bg-light' : '' }}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            @if($isVoteGapAlert)
                                                <h5 class="mb-1">
                                                    <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                                                    Vote Gap Alert: {{ $data['candidate_name'] }}
                                                    @if($isUnread)
                                                        <span class="badge bg-danger ms-2">New</span>
                                                    @endif
                                                </h5>
                                                <p class="mb-1">
                                                    {{ $data['is_leading'] 
                                                        ? "Lead has fallen to only {$data['gap']} votes against {$data['competitor_name']}." 
                                                        : "Trailing behind {$data['competitor_name']} by {$data['gap']} votes." 
                                                    }}
                                                </p>
                                                <small class="text-muted">
                                                    Position: {{ $data['position'] }} | 
                                                    Alert Threshold: {{ number_format($data['threshold']) }} votes | 
                                                    {{ $notification->created_at->diffForHumans() }}
                                                </small>
                                            @else
                                                <h5 class="mb-1">
                                                    <i class="bi bi-bell-fill text-primary me-2"></i>
                                                    Notification
                                                    @if($isUnread)
                                                        <span class="badge bg-danger ms-2">New</span>
                                                    @endif
                                                </h5>
                                                <p class="mb-1">{{ json_encode($data) }}</p>
                                                <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                                            @endif
                                        </div>
                                        
                                        <div class="d-flex">
                                            @if($isVoteGapAlert)
                                                <a href="{{ route('monitoring.compare', $data['candidate_id']) }}" class="btn btn-sm btn-primary me-2">
                                                    <i class="bi bi-bar-chart-line"></i> View Details
                                                </a>
                                            @endif
                                            
                                            @if($isUnread)
                                                <form action="{{ route('notifications.read', $notification->id) }}" method="POST">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-secondary">
                                                        <i class="bi bi-check"></i> Mark as Read
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="p-3">
                            {{ $notifications->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-bell-slash text-muted" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No Notifications</h4>
                            <p class="text-muted">You don't have any notifications at the moment.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

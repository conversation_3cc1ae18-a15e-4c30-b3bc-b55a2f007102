@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.2);
        transition: all 0.3s;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 165, 0, 0.3);
        color: white;
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .position-name {
        font-weight: 500;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .position-name i {
        color: #FFA500;
        margin-right: 10px;
        font-size: 1.1rem;
    }
    
    .position-badge {
        background-color: rgba(255, 165, 0, 0.1);
        color: #FFA500;
        font-size: 0.8rem;
        padding: 0.35rem 0.75rem;
        border-radius: 30px;
        font-weight: 500;
        display: inline-block;
        margin-left: 10px;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-candidates {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .btn-candidates:hover {
        background-color: #198754;
        color: white;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state-text {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 1.5rem;
    }
    
    /* Modal styling */
    .modal-content-custom {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .modal-header-custom {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        border: none;
        padding: 1.25rem 1.5rem;
    }
    
    .modal-title-custom {
        font-weight: 600;
        font-size: 1.25rem;
    }
    
    .modal-body-custom {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    
    .form-control-custom {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        border: 1px solid #dee2e6;
        transition: all 0.2s;
    }
    
    .form-control-custom:focus {
        border-color: #FFA500;
        box-shadow: 0 0 0 0.25rem rgba(255, 165, 0, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.3);
    }
    
    .btn-cancel {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background-color: #e9ecef;
    }
</style>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">Election Positions</h4>
            <p class="text-muted">Manage all election positions and their candidates</p>
        </div>
        <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_position">
            <i class="bi bi-plus-circle"></i> Add Position
        </button>
    </div>
    
    <!-- Main Content Card -->
    <div class="card card-custom">
        <div class="card-header-custom d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="bi bi-award me-2 text-warning"></i>
                <span class="fw-medium">All Positions</span>
            </div>
            <div class="input-group" style="width: 250px;">
                <input type="text" class="form-control" placeholder="Search positions..." id="searchInput">
                <span class="input-group-text bg-light">
                    <i class="bi bi-search"></i>
                </span>
            </div>
        </div>
        <div class="card-body-custom p-0">
            @if(count($positions) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Candidates</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($positions as $position)
                            <tr>
                                <td>
                                    <div class="position-name">
                                        <i class="bi bi-trophy"></i>
                                        <span>{{ $position->name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        {{ $position->candidates->count() }} candidates
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('positions.show',$position->id) }}" class="btn btn-action btn-candidates">
                                            <i class="bi bi-people"></i> Candidates
                                        </a>
                                        <a href="{{ route('positions.edit',$position->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        @if($position->candidates->count() == 0)
                                            <form action="{{ route('positions.destroy',$position->id) }}" method="post" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this position?')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>                            
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-award"></i>
                </div>
                <h5 class="empty-state-text">No positions found</h5>
                <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_position">
                    <i class="bi bi-plus-circle"></i> Add Your First Position
                </button>
            </div>
            @endif              
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="add_position" tabindex="-1" aria-labelledby="addPositionModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modal-content-custom">
      <div class="modal-header modal-header-custom">
        <h5 class="modal-title modal-title-custom" id="addPositionModalLabel">
            <i class="bi bi-plus-circle me-2"></i> Add New Position
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body modal-body-custom">
        <form action="{{ route('positions.store') }}" method="post">
            @csrf
            <div class="form-group">
                <label for="name" class="form-label">Position Name <span class="text-danger">*</span></label>
                <input type="text" name="name" id="name" class="form-control form-control-custom" placeholder="Enter position name (e.g. President, Governor)" required>
                <small class="text-muted">Enter the official title of the position</small>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-save">
                    <i class="bi bi-check2 me-1"></i> Save Position
                </button>
            </div>
        </form>
      </div>    
    </div>
  </div>
</div>

<script>
    // Simple search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('.table-custom tbody tr');
                
                tableRows.forEach(row => {
                    const positionName = row.querySelector('.position-name span').textContent.toLowerCase();
                    
                    if (positionName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }
    });
</script>
 
@endsection

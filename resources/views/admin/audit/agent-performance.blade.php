@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-person-badge text-primary me-2"></i>
                        Agent Performance: {{ $agent->user->name }}
                    </h2>
                    <p class="text-muted mb-0">
                        <i class="bi bi-telephone me-1"></i>{{ $agent->user->phone_number }}
                        <span class="mx-2">•</span>
                        <i class="bi bi-geo-alt me-1"></i>{{ $agent->polling_station->name }}
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.audit.agent', $agent) }}" class="btn btn-outline-info">
                        <i class="bi bi-list-ul me-1"></i>
                        Full Audit Trail
                    </a>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $performance['total_submissions'] }}</h3>
                    <p class="text-muted mb-0">Total Submissions</p>
                    <small class="text-muted">{{ $performance['avg_submissions_per_day'] }}/day avg</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-shield-check text-success fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $performance['reliability_score'] }}%</h3>
                    <p class="text-muted mb-0">Reliability Score</p>
                    <small class="text-{{ $performance['reliability_score'] >= 80 ? 'success' : ($performance['reliability_score'] >= 60 ? 'warning' : 'danger') }}">
                        {{ $performance['reliability_score'] >= 80 ? 'Excellent' : ($performance['reliability_score'] >= 60 ? 'Good' : 'Needs Improvement') }}
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-{{ $performance['flagged_submissions'] > 0 ? 'danger' : 'success' }} bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-flag text-{{ $performance['flagged_submissions'] > 0 ? 'danger' : 'success' }} fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $performance['flagged_submissions'] }}</h3>
                    <p class="text-muted mb-0">Flagged Submissions</p>
                    <small class="text-muted">{{ $performance['flag_rate'] }}% flag rate</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-check-circle text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $performance['verified_submissions'] }}</h3>
                    <p class="text-muted mb-0">Verified Submissions</p>
                    <small class="text-muted">{{ $performance['verification_rate'] }}% verified</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-clock me-2"></i>
                        Submission Pattern (By Hour)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="submissionPatternChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>
                        Performance Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">Reliability Score</span>
                            <span class="small">{{ $performance['reliability_score'] }}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-{{ $performance['reliability_score'] >= 80 ? 'success' : ($performance['reliability_score'] >= 60 ? 'warning' : 'danger') }}" 
                                 style="width: {{ $performance['reliability_score'] }}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">Verification Rate</span>
                            <span class="small">{{ $performance['verification_rate'] }}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: {{ $performance['verification_rate'] }}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">Flag Rate</span>
                            <span class="small">{{ $performance['flag_rate'] }}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-danger" style="width: {{ min($performance['flag_rate'], 100) }}%"></div>
                        </div>
                    </div>

                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="fw-bold text-primary">{{ $performance['avg_submissions_per_day'] }}</div>
                            <small class="text-muted">Avg/Day</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-success">{{ count($performance['submission_hours']) }}</div>
                            <small class="text-muted">Active Hours</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Audit Trail -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        Recent Audit Trail
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Submission Time</th>
                                    <th>Candidate</th>
                                    <th>Vote Change</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($auditTrail->take(20) as $log)
                                <tr class="{{ $log->is_flagged ? 'table-danger' : ($log->is_verified ? 'table-success' : '') }}">
                                    <td>
                                        <div class="fw-semibold">{{ $log->submission_time->format('M j, Y H:i') }}</div>
                                        <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $log->candidate->name }}</div>
                                        <small class="text-muted">{{ $log->candidate->position->name }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ number_format($log->new_votes) }}</span>
                                        @if($log->vote_difference != 0)
                                            <div class="small text-{{ $log->vote_difference > 0 ? 'success' : 'danger' }}">
                                                {{ $log->vote_difference > 0 ? '+' : '' }}{{ number_format($log->vote_difference) }}
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $log->submission_method === 'api' ? 'success' : 'info' }}">
                                            {{ ucfirst($log->submission_method) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($log->is_flagged)
                                            <span class="badge bg-danger">
                                                <i class="bi bi-flag me-1"></i>Flagged
                                            </span>
                                            @if($log->flag_reason)
                                                <div class="small text-muted">{{ $log->flag_reason }}</div>
                                            @endif
                                        @elseif($log->is_verified)
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Verified
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">Pending</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @if(!$log->is_verified)
                                                <button class="btn btn-outline-success btn-sm" onclick="verifySubmission({{ $log->id }})">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            @endif
                                            @if(!$log->is_flagged)
                                                <button class="btn btn-outline-warning btn-sm" onclick="flagSubmission({{ $log->id }})">
                                                    <i class="bi bi-flag"></i>
                                                </button>
                                            @else
                                                <button class="btn btn-outline-info btn-sm" onclick="unflagSubmission({{ $log->id }})">
                                                    <i class="bi bi-flag-fill"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                            No audit trail found for this agent in the selected period.
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($auditTrail->count() > 20)
                <div class="card-footer text-center">
                    <a href="{{ route('admin.audit.agent', $agent) }}" class="btn btn-outline-primary">
                        <i class="bi bi-eye me-1"></i>
                        View Full Audit Trail ({{ $auditTrail->count() }} total)
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Submission pattern chart
    const ctx = document.getElementById('submissionPatternChart').getContext('2d');
    const submissionHours = @json($performance['submission_hours']);
    
    // Create 24-hour array with submission counts
    const hourlyData = Array.from({length: 24}, (_, i) => submissionHours[i] || 0);
    const hourLabels = Array.from({length: 24}, (_, i) => i + ':00');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hourLabels,
            datasets: [{
                label: 'Submissions',
                data: hourlyData,
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});

function verifySubmission(logId) {
    if (confirm('Are you sure you want to verify this submission?')) {
        fetch(`/admin/audit/verify/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error verifying submission');
            }
        });
    }
}

function flagSubmission(logId) {
    const reason = prompt('Enter reason for flagging this submission:');
    if (reason) {
        fetch(`/admin/audit/flag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error flagging submission');
            }
        });
    }
}

function unflagSubmission(logId) {
    if (confirm('Are you sure you want to remove the flag from this submission?')) {
        fetch(`/admin/audit/unflag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error unflagging submission');
            }
        });
    }
}
</script>
@endsection

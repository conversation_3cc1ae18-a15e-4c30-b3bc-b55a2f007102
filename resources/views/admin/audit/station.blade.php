@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-building text-primary me-2"></i>
                        Station Audit Trail: {{ $station->name }}
                    </h2>
                    <p class="text-muted mb-0">
                        <i class="bi bi-geo-alt me-1"></i>{{ $station->parish }}, {{ $station->subcounty }}, {{ $station->county }}, {{ $station->district }}
                        <span class="mx-2">•</span>
                        <i class="bi bi-people me-1"></i>{{ $station->registered_voters }} registered voters
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('polling_stations.show', $station) }}" class="btn btn-outline-info">
                        <i class="bi bi-eye me-1"></i>
                        View Station
                    </a>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Station Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $auditTrail->count() }}</h3>
                    <p class="text-muted mb-0">Total Submissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-people text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $auditTrail->unique('agent_id')->count() }}</h3>
                    <p class="text-muted mb-0">Active Agents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-flag text-warning fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $auditTrail->where('is_flagged', true)->count() }}</h3>
                    <p class="text-muted mb-0">Flagged</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-check-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $auditTrail->where('is_verified', true)->count() }}</h3>
                    <p class="text-muted mb-0">Verified</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2">
                    <label for="agent" class="form-label">Agent</label>
                    <select class="form-select" id="agent" name="agent">
                        <option value="">All Agents</option>
                        @foreach($auditTrail->unique('agent_id') as $log)
                            <option value="{{ $log->agent_id }}" {{ request('agent') == $log->agent_id ? 'selected' : '' }}>
                                {{ $log->agent->user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="flagged" {{ request('status') == 'flagged' ? 'selected' : '' }}>Flagged</option>
                        <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>Verified</option>
                        <option value="unverified" {{ request('status') == 'unverified' ? 'selected' : '' }}>Unverified</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="method" class="form-label">Method</label>
                    <select class="form-select" id="method" name="method">
                        <option value="">All Methods</option>
                        <option value="api" {{ request('method') == 'api' ? 'selected' : '' }}>Mobile App</option>
                        <option value="web" {{ request('method') == 'web' ? 'selected' : '' }}>Web Portal</option>
                        <option value="manager_portal" {{ request('method') == 'manager_portal' ? 'selected' : '' }}>Manager Portal</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search me-1"></i>
                        Filter
                    </button>
                    <a href="{{ route('admin.audit.station', $station) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Audit Trail Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Station Audit Trail
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Submission Time</th>
                            <th>Agent</th>
                            <th>Candidate</th>
                            <th>Vote Change</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($auditTrail as $log)
                        <tr class="{{ $log->is_flagged ? 'table-danger' : ($log->is_verified ? 'table-success' : '') }}">
                            <td>
                                <div class="fw-semibold">{{ $log->submission_time->format('M j, Y H:i') }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->agent->user->name }}</div>
                                <small class="text-muted">{{ $log->agent->user->phone_number }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->candidate->name }}</div>
                                <small class="text-muted">{{ $log->candidate->position->name }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ number_format($log->new_votes) }}</span>
                                @if($log->vote_difference != 0)
                                    <div class="small text-{{ $log->vote_difference > 0 ? 'success' : 'danger' }}">
                                        {{ $log->vote_difference > 0 ? '+' : '' }}{{ number_format($log->vote_difference) }}
                                    </div>
                                @endif
                                @if($log->previous_votes > 0)
                                    <small class="text-muted d-block">Previous: {{ number_format($log->previous_votes) }}</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->submission_method === 'api' ? 'success' : 'info' }}">
                                    {{ ucfirst($log->submission_method) }}
                                </span>
                                @if($log->ip_address)
                                    <div class="small text-muted">{{ $log->ip_address }}</div>
                                @endif
                            </td>
                            <td>
                                @if($log->is_flagged)
                                    <span class="badge bg-danger">
                                        <i class="bi bi-flag me-1"></i>Flagged
                                    </span>
                                    @if($log->flag_reason)
                                        <div class="small text-muted">{{ $log->flag_reason }}</div>
                                    @endif
                                @elseif($log->is_verified)
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>Verified
                                    </span>
                                @else
                                    <span class="badge bg-secondary">Pending</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}" 
                                       class="btn btn-outline-primary btn-sm" 
                                       title="Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>
                                    
                                    @if(!$log->is_verified)
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="verifySubmission({{ $log->id }})" 
                                                title="Verify">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    @endif
                                    
                                    @if(!$log->is_flagged)
                                        <button class="btn btn-outline-warning btn-sm" 
                                                onclick="flagSubmission({{ $log->id }})" 
                                                title="Flag">
                                            <i class="bi bi-flag"></i>
                                        </button>
                                    @else
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="unflagSubmission({{ $log->id }})" 
                                                title="Remove Flag">
                                            <i class="bi bi-flag-fill"></i>
                                        </button>
                                    @endif
                                    
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="viewDetails({{ $log->id }})" 
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4"></i>
                                    <h5 class="mt-3">No Audit Trail Found</h5>
                                    <p class="mb-0">This polling station has no recorded submissions in the selected period.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Station Statistics -->
    @if($auditTrail->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        Station Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">Submission Activity</h6>
                            <ul class="list-unstyled">
                                <li><strong>Total Submissions:</strong> {{ $auditTrail->count() }}</li>
                                <li><strong>Unique Agents:</strong> {{ $auditTrail->unique('agent_id')->count() }}</li>
                                <li><strong>First Submission:</strong> {{ $auditTrail->last()->submission_time->format('M j, Y H:i') }}</li>
                                <li><strong>Last Submission:</strong> {{ $auditTrail->first()->submission_time->format('M j, Y H:i') }}</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-success">Quality Metrics</h6>
                            <ul class="list-unstyled">
                                <li><strong>Verified:</strong> {{ $auditTrail->where('is_verified', true)->count() }} ({{ round(($auditTrail->where('is_verified', true)->count() / $auditTrail->count()) * 100, 1) }}%)</li>
                                <li><strong>Flagged:</strong> {{ $auditTrail->where('is_flagged', true)->count() }} ({{ round(($auditTrail->where('is_flagged', true)->count() / $auditTrail->count()) * 100, 1) }}%)</li>
                                <li><strong>Pending:</strong> {{ $auditTrail->where('is_verified', false)->where('is_flagged', false)->count() }}</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-info">Submission Methods</h6>
                            <ul class="list-unstyled">
                                <li><strong>Mobile App:</strong> {{ $auditTrail->where('submission_method', 'api')->count() }}</li>
                                <li><strong>Web Portal:</strong> {{ $auditTrail->where('submission_method', 'web')->count() }}</li>
                                <li><strong>Manager Portal:</strong> {{ $auditTrail->where('submission_method', 'manager_portal')->count() }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function verifySubmission(logId) {
    if (confirm('Are you sure you want to verify this submission?')) {
        fetch(`/admin/audit/verify/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error verifying submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error verifying submission');
        });
    }
}

function flagSubmission(logId) {
    const reason = prompt('Enter reason for flagging this submission:');
    if (reason) {
        fetch(`/admin/audit/flag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error flagging submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error flagging submission');
        });
    }
}

function unflagSubmission(logId) {
    const notes = prompt('Enter notes for removing this flag (optional):');
    if (notes !== null) {
        fetch(`/admin/audit/unflag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing flag');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing flag');
        });
    }
}

function viewDetails(logId) {
    window.location.href = `/admin/audit?search=${logId}`;
}
</script>
@endsection

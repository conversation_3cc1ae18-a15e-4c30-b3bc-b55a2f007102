@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card audit-header-card">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="header-content">
                            <h4 class="mb-1 fw-bold text-dark">
                                <i class="bi bi-shield-check me-2 text-gold"></i>
                                Vote Audit Dashboard
                            </h4>
                            <p class="text-muted mb-0 small">Monitor and review vote submissions</p>
                        </div>
                        <div class="d-flex gap-1 flex-wrap">
                            <a href="{{ route('admin.audit.activity') }}" class="btn btn-compact btn-outline-gold">
                                <i class="bi bi-activity"></i>
                                <span class="d-none d-md-inline ms-1">Activity</span>
                            </a>
                            <a href="{{ route('admin.audit.statistics') }}" class="btn btn-compact btn-outline-gold">
                                <i class="bi bi-graph-up"></i>
                                <span class="d-none d-md-inline ms-1">Stats</span>
                            </a>
                            <a href="{{ route('admin.audit.flagged') }}" class="btn btn-compact btn-outline-gold position-relative">
                                <i class="bi bi-flag"></i>
                                <span class="d-none d-md-inline ms-1">Flagged</span>
                                @if($suspiciousActivity['total_flagged'] > 0)
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {{ $suspiciousActivity['total_flagged'] }}
                                </span>
                                @endif
                            </a>
                            <button class="btn btn-compact btn-outline-gold" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span class="d-none d-lg-inline ms-1">Refresh</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card audit-stat-card audit-danger hover-lift">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 stat-number">{{ $suspiciousActivity['total_flagged'] }}</h3>
                            <p class="mb-0 stat-label">Flagged Submissions</p>
                        </div>
                        <div class="stat-icon stat-icon-danger">
                            <i class="bi bi-flag"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card audit-stat-card audit-warning hover-lift">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 stat-number">{{ $suspiciousActivity['rapid_submissions'] }}</h3>
                            <p class="mb-0 stat-label">Rapid Submissions</p>
                        </div>
                        <div class="stat-icon stat-icon-warning">
                            <i class="bi bi-lightning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card audit-stat-card audit-info hover-lift">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 stat-number">{{ $suspiciousActivity['large_changes'] }}</h3>
                            <p class="mb-0 stat-label">Large Vote Changes</p>
                        </div>
                        <div class="stat-icon stat-icon-info">
                            <i class="bi bi-arrow-up-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card audit-stat-card audit-secondary hover-lift">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 stat-number">{{ $suspiciousActivity['agents_with_multiple_submissions'] }}</h3>
                            <p class="mb-0 stat-label">Multiple Submissions</p>
                        </div>
                        <div class="stat-icon stat-icon-secondary">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Filters Section -->
    <div class="card compact-filters-card mb-3">
        <div class="card-body p-3">
            <form method="GET" class="row g-2 align-items-end">
                <div class="col-md-3">
                    <label for="search" class="form-label-compact">
                        <i class="bi bi-search me-1 text-gold"></i>Search
                    </label>
                    <input type="text" class="form-control form-control-compact" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Agent, station, candidate...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label-compact">
                        <i class="bi bi-check-circle me-1 text-gold"></i>Status
                    </label>
                    <select class="form-select form-control-compact" id="status" name="status">
                        <option value="">All</option>
                        <option value="flagged" {{ request('status') == 'flagged' ? 'selected' : '' }}>Flagged</option>
                        <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>Verified</option>
                        <option value="unverified" {{ request('status') == 'unverified' ? 'selected' : '' }}>Unverified</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="agent_id" class="form-label-compact">
                        <i class="bi bi-person me-1 text-gold"></i>Agent
                    </label>
                    <select class="form-select form-control-compact" id="agent_id" name="agent_id">
                        <option value="">All</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                {{ $agent->user->name ?? 'Unknown' }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label-compact">
                        <i class="bi bi-calendar me-1 text-gold"></i>From
                    </label>
                    <input type="date" class="form-control form-control-compact" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label-compact">
                        <i class="bi bi-calendar me-1 text-gold"></i>To
                    </label>
                    <input type="date" class="form-control form-control-compact" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-1">
                    <label class="form-label-compact">&nbsp;</label>
                    <button type="submit" class="btn btn-compact btn-outline-gold w-100">
                        <i class="bi bi-search"></i>
                        <span class="d-none d-lg-inline ms-1">Filter</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Compact Audit Logs Table -->
    <div class="card compact-table-card">
        <div class="card-header-compact">
            <h6 class="mb-0 fw-semibold">
                <i class="bi bi-list-ul me-2 text-gold"></i>
                Audit Trail
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-compact mb-0">
                    <thead>
                        <tr>
                            <th><i class="bi bi-clock"></i> Time</th>
                            <th><i class="bi bi-person"></i> Agent</th>
                            <th><i class="bi bi-geo-alt"></i> Station</th>
                            <th><i class="bi bi-person-check"></i> Candidate</th>
                            <th><i class="bi bi-graph-up"></i> Votes</th>
                            <th><i class="bi bi-laptop"></i> Method</th>
                            <th><i class="bi bi-shield-check"></i> Status</th>
                            <th><i class="bi bi-gear"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($auditLogs as $log)
                        <tr class="{{ $log->is_flagged ? 'table-danger' : ($log->is_verified ? 'table-success' : '') }}">
                            <td>
                                <div class="fw-semibold">{{ $log->formatted_submission_time }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->agent->user->name ?? 'Unknown' }}</div>
                                <small class="text-muted">{{ Str::limit($log->agent->user->phone_number ?? 'N/A', 12) }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ Str::limit($log->pollingStation->name, 20) }}</div>
                                <small class="text-muted">{{ Str::limit($log->pollingStation->district, 15) }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ Str::limit($log->candidate->name, 18) }}</div>
                                <small class="text-muted">{{ Str::limit($log->candidate->position->name, 15) }}</small>
                            </td>
                            <td>
                                <div class="fw-bold {{ $log->vote_difference > 0 ? 'text-success' : ($log->vote_difference < 0 ? 'text-danger' : 'text-muted') }}">
                                    @if($log->action_type === 'create')
                                        <span class="badge bg-primary px-1 py-0" style="font-size: 0.6rem;">NEW</span> {{ $log->new_votes }}
                                    @else
                                        @if($log->vote_difference > 0)
                                            <i class="bi bi-arrow-up"></i>{{ $log->vote_difference }}
                                        @elseif($log->vote_difference < 0)
                                            <i class="bi bi-arrow-down"></i>{{ abs($log->vote_difference) }}
                                        @else
                                            <i class="bi bi-dash"></i>0
                                        @endif
                                    @endif
                                </div>
                                <small class="text-muted">{{ $log->new_votes }} total</small>
                            </td>
                            <td>
                                <span class="status-badge
                                    {{ $log->submission_method === 'api' ? 'status-info' :
                                       ($log->submission_method === 'manager_portal' ? 'status-warning' : 'status-secondary') }}">
                                    <i class="bi bi-{{ $log->submission_method === 'api' ? 'phone' :
                                                      ($log->submission_method === 'manager_portal' ? 'laptop' : 'globe') }}"></i>
                                    {{ ucfirst(str_replace('_', ' ', $log->submission_method)) }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge
                                    {{ $log->is_flagged ? 'status-flagged' :
                                       ($log->is_verified ? 'status-verified' : 'status-unverified') }}">
                                    <i class="bi bi-{{ $log->is_flagged ? 'flag-fill' :
                                                      ($log->is_verified ? 'check-circle-fill' : 'clock') }}"></i>
                                    {{ $log->status_text }}
                                </span>
                                @if($log->is_flagged)
                                    <div class="mt-1">
                                        <small class="text-danger fw-semibold" style="font-size: 0.65rem;">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            {{ Str::limit(ucfirst(str_replace('_', ' ', $log->flag_reason)), 20) }}
                                        </small>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex gap-1" role="group">
                                    @if(!$log->is_verified)
                                        <button class="btn-action btn-verify"
                                                onclick="verifySubmission({{ $log->id }})"
                                                title="Verify Submission">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                    @endif

                                    @if(!$log->is_flagged)
                                        <button class="btn-action btn-flag"
                                                onclick="flagSubmission({{ $log->id }})"
                                                title="Flag as Suspicious">
                                            <i class="bi bi-flag"></i>
                                        </button>
                                    @else
                                        <button class="btn-action btn-unflag"
                                                onclick="unflagSubmission({{ $log->id }})"
                                                title="Remove Flag">
                                            <i class="bi bi-flag-fill"></i>
                                        </button>
                                    @endif

                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}"
                                       class="btn-action btn-view"
                                       title="View Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>

                                    <button class="btn-action btn-view"
                                            onclick="viewDetails({{ $log->id }})"
                                            title="View Full Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">No audit logs found</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($auditLogs->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $auditLogs->firstItem() }} to {{ $auditLogs->lastItem() }} 
                    of {{ $auditLogs->total() }} audit logs
                </div>
                <div>
                    {{ $auditLogs->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Modals will be added here -->
<div id="modalContainer"></div>

<script>
function refreshData() {
    window.location.reload();
}

function verifySubmission(logId) {
    // Implementation for verify modal
    console.log('Verify submission:', logId);
}

function flagSubmission(logId) {
    // Implementation for flag modal
    console.log('Flag submission:', logId);
}

function unflagSubmission(logId) {
    // Implementation for unflag modal
    console.log('Unflag submission:', logId);
}

function viewDetails(logId) {
    // Implementation for details modal
    console.log('View details:', logId);
}
</script>

<style>
/* ===== AUDIT PAGE ENHANCED STYLING ===== */

/* Compact Header Card */
.audit-header-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-left: 4px solid #FFA500;
    transition: all 0.2s ease;
}

.audit-header-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.header-content h4 {
    font-size: 1.4rem;
    line-height: 1.2;
}

.header-content p {
    font-size: 0.85rem;
    line-height: 1.3;
}

/* Compact Buttons */
.btn-compact {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-width: 1px;
}

.btn-outline-gold {
    border-color: #FFA500;
    color: #FFA500;
    background: transparent;
}

.btn-outline-gold:hover {
    background: #FFA500;
    border-color: #FFA500;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
}

.btn-outline-gold i {
    font-size: 0.9rem;
}

/* Badge for flagged count */
.btn-compact .badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

/* Compact Filters */
.compact-filters-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
    background: #fafbfc;
    border-left: 3px solid #FFA500;
}

.form-label-compact {
    font-size: 0.75rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.form-control-compact {
    padding: 0.4rem 0.75rem;
    font-size: 0.85rem;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    transition: all 0.2s ease;
    height: auto;
}

.form-control-compact:focus {
    border-color: #FFA500;
    box-shadow: 0 0 0 0.15rem rgba(255, 165, 0, 0.15);
}

.form-control-compact::placeholder {
    font-size: 0.8rem;
    color: #9ca3af;
}

/* Compact Table */
.compact-table-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.card-header-compact {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.table-compact {
    font-size: 0.8rem;
    margin-bottom: 0;
}

.table-compact thead th {
    background: #ffffff;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 0.6rem 0.75rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 2px solid #f1f3f4;
}

.table-compact thead th i {
    font-size: 0.7rem;
    margin-right: 0.25rem;
    color: #FFA500;
}

.table-compact tbody tr {
    transition: all 0.15s ease;
    border: none;
}

.table-compact tbody tr:hover {
    background: rgba(255, 165, 0, 0.03);
}

.table-compact tbody td {
    padding: 0.6rem 0.75rem;
    border: none;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
    line-height: 1.3;
}

/* Compact Status Badges */
.table-compact .status-badge {
    padding: 0.25rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* Compact Action Buttons */
.table-compact .btn-action {
    padding: 0.3rem;
    border-radius: 6px;
    border: none;
    transition: all 0.15s ease;
    margin: 0 0.1rem;
    font-size: 0.7rem;
}

.table-compact .btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Audit Statistics Cards */
.audit-stat-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.audit-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
}

.audit-danger::before {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

.audit-warning::before {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.audit-info::before {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.audit-secondary::before {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-top: 0.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

.stat-icon-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.stat-icon-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.stat-icon-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Enhanced Form Controls */
.form-control-enhanced {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control-enhanced:focus {
    border-color: #FFA500;
    box-shadow: 0 0 0 0.25rem rgba(255, 165, 0, 0.15);
    background: #fff;
}

.form-control-enhanced:hover {
    border-color: #FFD700;
}

/* Enhanced Table Styling */
.table-enhanced {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-enhanced tbody tr {
    transition: all 0.2s ease;
    border: none;
}

.table-enhanced tbody tr:hover {
    background: rgba(255, 165, 0, 0.05);
    transform: scale(1.01);
}

.table-enhanced tbody td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #f1f3f4;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-flagged {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-verified {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-unverified {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.status-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-secondary {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(73, 80, 87, 0.1) 100%);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Action Buttons */
.btn-action {
    padding: 0.5rem;
    border-radius: 8px;
    border: none;
    transition: all 0.2s ease;
    margin: 0 0.25rem;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-verify {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-flag {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
}

.btn-unflag {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.btn-view {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .audit-stat-card .card-body {
        padding: 1rem;
    }

    .table-enhanced {
        font-size: 0.9rem;
    }

    .table-enhanced thead th,
    .table-enhanced tbody td {
        padding: 0.75rem 0.5rem;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}
</style>
@endsection

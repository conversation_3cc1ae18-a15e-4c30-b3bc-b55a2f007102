@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-question-circle text-info me-2"></i>
                        Unverified Submissions
                    </h2>
                    <p class="text-muted mb-0">Review and verify submissions that are pending verification</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="verifyAllVisible()">
                        <i class="bi bi-check-all me-1"></i>
                        Verify All Visible
                    </button>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-info shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-question-circle text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $unverifiedSubmissions->total() }}</h3>
                    <p class="text-muted mb-0">Unverified Submissions</p>
                    <small class="text-muted">Pending review and verification</small>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        Verification Guidelines
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li class="mb-1"><i class="bi bi-check text-success me-2"></i>Verify submissions that appear legitimate</li>
                                <li class="mb-1"><i class="bi bi-flag text-warning me-2"></i>Flag suspicious patterns or large changes</li>
                                <li class="mb-1"><i class="bi bi-eye text-info me-2"></i>Review agent performance history</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li class="mb-1"><i class="bi bi-clock text-secondary me-2"></i>Check submission timing</li>
                                <li class="mb-1"><i class="bi bi-geo-alt text-primary me-2"></i>Verify location consistency</li>
                                <li class="mb-1"><i class="bi bi-phone text-success me-2"></i>Consider submission method</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Unverified Submissions Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    Submissions Pending Verification
                </h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleAllCheckboxes()">
                    <label class="form-check-label small" for="selectAll">
                        Select All
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="headerCheckbox" onchange="toggleAllCheckboxes()">
                            </th>
                            <th>Submission Time</th>
                            <th>Agent</th>
                            <th>Station</th>
                            <th>Candidate</th>
                            <th>Vote Change</th>
                            <th>Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($unverifiedSubmissions as $log)
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input submission-checkbox" value="{{ $log->id }}">
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->submission_time->format('M j, Y H:i') }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->agent->user->name }}</div>
                                <small class="text-muted">{{ $log->agent->user->phone_number }}</small>
                            </td>
                            <td>{{ $log->pollingStation->name }}</td>
                            <td>
                                <div class="fw-semibold">{{ $log->candidate->name }}</div>
                                <small class="text-muted">{{ $log->candidate->position->name }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ number_format($log->new_votes) }}</span>
                                @if($log->vote_difference != 0)
                                    <div class="small text-{{ $log->vote_difference > 0 ? 'success' : 'danger' }}">
                                        {{ $log->vote_difference > 0 ? '+' : '' }}{{ number_format($log->vote_difference) }}
                                    </div>
                                @endif
                                @if($log->previous_votes > 0)
                                    <small class="text-muted d-block">Previous: {{ number_format($log->previous_votes) }}</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->submission_method === 'api' ? 'success' : 'info' }}">
                                    {{ ucfirst($log->submission_method) }}
                                </span>
                                @if($log->ip_address)
                                    <div class="small text-muted">{{ $log->ip_address }}</div>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}" 
                                       class="btn btn-outline-primary btn-sm" 
                                       title="Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>
                                    
                                    <button class="btn btn-outline-success btn-sm" 
                                            onclick="verifySubmission({{ $log->id }})" 
                                            title="Verify">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    
                                    <button class="btn btn-outline-warning btn-sm" 
                                            onclick="flagSubmission({{ $log->id }})" 
                                            title="Flag as Suspicious">
                                        <i class="bi bi-flag"></i>
                                    </button>
                                    
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="viewDetails({{ $log->id }})" 
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-check-circle display-4 text-success"></i>
                                    <h5 class="mt-3">All Submissions Verified</h5>
                                    <p class="mb-0">Great! All submissions have been reviewed and verified.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($unverifiedSubmissions->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $unverifiedSubmissions->firstItem() }} to {{ $unverifiedSubmissions->lastItem() }} 
                    of {{ $unverifiedSubmissions->total() }} unverified submissions
                </div>
                <div>
                    {{ $unverifiedSubmissions->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Bulk Actions -->
    @if($unverifiedSubmissions->count() > 0)
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h6 class="text-primary mb-3">
                <i class="bi bi-lightning me-2"></i>
                Bulk Actions
            </h6>
            <div class="d-flex gap-2">
                <button class="btn btn-success" onclick="verifySelected()">
                    <i class="bi bi-check-all me-1"></i>
                    Verify Selected
                </button>
                <button class="btn btn-warning" onclick="flagSelected()">
                    <i class="bi bi-flag me-1"></i>
                    Flag Selected
                </button>
                <span class="text-muted small align-self-center ms-3">
                    <span id="selectedCount">0</span> submissions selected
                </span>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function toggleAllCheckboxes() {
    const headerCheckbox = document.getElementById('headerCheckbox');
    const checkboxes = document.querySelectorAll('.submission-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = headerCheckbox.checked;
    });
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.submission-checkbox:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

// Update count when individual checkboxes are clicked
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('submission-checkbox')) {
        updateSelectedCount();
    }
});

function verifySubmission(logId) {
    if (confirm('Are you sure you want to verify this submission?')) {
        fetch(`/admin/audit/verify/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error verifying submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error verifying submission');
        });
    }
}

function flagSubmission(logId) {
    const reason = prompt('Enter reason for flagging this submission:');
    if (reason) {
        fetch(`/admin/audit/flag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error flagging submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error flagging submission');
        });
    }
}

function verifySelected() {
    const selectedCheckboxes = document.querySelectorAll('.submission-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Please select submissions to verify');
        return;
    }
    
    if (confirm(`Are you sure you want to verify ${selectedIds.length} submissions?`)) {
        // Process each verification
        Promise.all(selectedIds.map(id => 
            fetch(`/admin/audit/verify/${id}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
        ))
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error verifying submissions');
        });
    }
}

function flagSelected() {
    const selectedCheckboxes = document.querySelectorAll('.submission-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Please select submissions to flag');
        return;
    }
    
    const reason = prompt('Enter reason for flagging these submissions:');
    if (reason) {
        Promise.all(selectedIds.map(id => 
            fetch(`/admin/audit/flag/${id}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ reason: reason })
            })
        ))
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error flagging submissions');
        });
    }
}

function verifyAllVisible() {
    const allCheckboxes = document.querySelectorAll('.submission-checkbox');
    allCheckboxes.forEach(cb => cb.checked = true);
    updateSelectedCount();
    verifySelected();
}

function viewDetails(logId) {
    window.location.href = `/admin/audit?search=${logId}`;
}
</script>
@endsection

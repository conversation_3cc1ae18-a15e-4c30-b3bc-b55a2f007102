@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="bi bi-shield-check text-primary me-2"></i>
                        Vote Audit System Demo
                    </h3>
                    <p class="text-muted mb-0">Comprehensive audit trail for all vote submissions</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="bi bi-list-check text-success me-2"></i>Features Implemented</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Complete Audit Trail:</strong> Every vote submission is logged with full details
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Multiple Submissions Tracking:</strong> All changes and updates are recorded
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Automatic Flagging:</strong> Suspicious activities are auto-detected
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Vote Difference Tracking:</strong> Shows exactly what changed between submissions
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Submission Method Tracking:</strong> API, Web, or Manager Portal
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>User Attribution:</strong> Who submitted what and when
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Verification System:</strong> Admins can verify or flag submissions
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <strong>Location Tracking:</strong> GPS coordinates for each submission
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="bi bi-flag text-warning me-2"></i>Automatic Flagging Criteria</h5>
                            <div class="alert alert-warning">
                                <h6>The system automatically flags submissions for:</h6>
                                <ul class="mb-0">
                                    <li><strong>Rapid Multiple Submissions:</strong> More than 3 submissions in 10 minutes</li>
                                    <li><strong>Large Vote Changes:</strong> Vote differences greater than 1000</li>
                                    <li><strong>Unusual Timing:</strong> Submissions outside 6 AM - 10 PM</li>
                                    <li><strong>Manual Flags:</strong> Admin-initiated flags for investigation</li>
                                </ul>
                            </div>

                            <h5><i class="bi bi-database text-info me-2"></i>Data Captured</h5>
                            <div class="alert alert-info">
                                <h6>Each audit log entry includes:</h6>
                                <ul class="mb-0">
                                    <li>Agent and candidate information</li>
                                    <li>Previous and new vote counts</li>
                                    <li>Vote difference calculation</li>
                                    <li>Submission timestamp and method</li>
                                    <li>User who made the submission</li>
                                    <li>IP address and user agent</li>
                                    <li>GPS coordinates (if available)</li>
                                    <li>Verification and flagging status</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-12">
                            <h5><i class="bi bi-gear text-primary me-2"></i>How It Works</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="bi bi-upload display-4 text-primary"></i>
                                            <h6 class="mt-2">1. Vote Submission</h6>
                                            <p class="small text-muted">Agent submits votes via mobile app or manager portal</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="bi bi-journal-text display-4 text-success"></i>
                                            <h6 class="mt-2">2. Automatic Logging</h6>
                                            <p class="small text-muted">System automatically creates detailed audit log entry</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <i class="bi bi-shield-check display-4 text-warning"></i>
                                            <h6 class="mt-2">3. Analysis & Flagging</h6>
                                            <p class="small text-muted">Automatic analysis flags suspicious activities</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-12">
                            <h5><i class="bi bi-eye text-info me-2"></i>Access Points</h5>
                            <div class="d-flex gap-3 flex-wrap">
                                <a href="{{ route('admin.audit.index') }}" class="btn btn-primary">
                                    <i class="bi bi-list-ul me-1"></i>
                                    Main Audit Dashboard
                                </a>
                                <a href="{{ route('admin.audit.flagged') }}" class="btn btn-warning">
                                    <i class="bi bi-flag me-1"></i>
                                    Flagged Submissions
                                </a>
                                <a href="{{ route('admin.audit.unverified') }}" class="btn btn-info">
                                    <i class="bi bi-question-circle me-1"></i>
                                    Unverified Submissions
                                </a>
                                <a href="{{ route('admin.audit.statistics') }}" class="btn btn-success">
                                    <i class="bi bi-graph-up me-1"></i>
                                    Audit Statistics
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle me-2"></i>System Benefits</h6>
                                <ul class="mb-0">
                                    <li><strong>Transparency:</strong> Complete visibility into all vote submissions</li>
                                    <li><strong>Accountability:</strong> Every action is tracked and attributed</li>
                                    <li><strong>Security:</strong> Automatic detection of suspicious activities</li>
                                    <li><strong>Compliance:</strong> Comprehensive audit trail for election oversight</li>
                                    <li><strong>Investigation:</strong> Detailed logs for dispute resolution</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

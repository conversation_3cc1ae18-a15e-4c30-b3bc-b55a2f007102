@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-graph-up text-primary me-2"></i>
                        Audit Statistics
                    </h2>
                    <p class="text-muted mb-0">Comprehensive analytics and reporting for vote submission audit data</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.audit.activity') }}" class="btn btn-outline-success">
                        <i class="bi bi-activity me-1"></i>
                        Live Activity
                    </a>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="{{ $startDate->format('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="{{ $endDate->format('Y-m-d') }}">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        Update Statistics
                    </button>
                </div>
                <div class="col-md-3 text-end">
                    <small class="text-muted">
                        Period: {{ $startDate->format('M j, Y') }} - {{ $endDate->format('M j, Y') }}
                        ({{ $startDate->diffInDays($endDate) + 1 }} days)
                    </small>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($statistics['total_submissions']) }}</h3>
                    <p class="text-muted mb-0">Total Submissions</p>
                    <small class="text-muted">
                        {{ round($statistics['total_submissions'] / max($startDate->diffInDays($endDate), 1), 1) }}/day avg
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-people text-success fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($statistics['unique_agents']) }}</h3>
                    <p class="text-muted mb-0">Active Agents</p>
                    <small class="text-muted">
                        {{ round($statistics['total_submissions'] / max($statistics['unique_agents'], 1), 1) }} submissions/agent
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-flag text-warning fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($statistics['flagged_submissions']) }}</h3>
                    <p class="text-muted mb-0">Flagged Submissions</p>
                    <small class="text-muted">
                        {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}% of total
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-check-circle text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ number_format($statistics['verified_submissions']) }}</h3>
                    <p class="text-muted mb-0">Verified Submissions</p>
                    <small class="text-muted">
                        {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}% of total
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Submission Methods and Geographic Distribution -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart me-2"></i>
                        Submission Methods
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-phone text-success fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['api_submissions']) }}</h4>
                                <p class="text-muted mb-0">Mobile App</p>
                                <small class="text-muted">
                                    {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                </small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-globe text-info fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['web_submissions']) }}</h4>
                                <p class="text-muted mb-0">Web Portal</p>
                                <small class="text-muted">
                                    {{ $statistics['total_submissions'] > 0 ? round(($statistics['web_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                </small>
                            </div>
                        </div>
                    </div>
                    @if($statistics['manager_submissions'] > 0)
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center p-3 border-top">
                                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 50px; height: 50px;">
                                    <i class="bi bi-person-gear text-warning fs-5"></i>
                                </div>
                                <h5 class="mb-1">{{ number_format($statistics['manager_submissions']) }}</h5>
                                <p class="text-muted mb-0">Manager Portal</p>
                                <small class="text-muted">
                                    {{ $statistics['total_submissions'] > 0 ? round(($statistics['manager_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                </small>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-building me-2"></i>
                        Geographic Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-geo-alt text-primary fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['unique_stations']) }}</h4>
                                <p class="text-muted mb-0">Polling Stations</p>
                                <small class="text-muted">Active locations</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-calculator text-secondary fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['average_votes_per_submission'] ?? 0) }}</h4>
                                <p class="text-muted mb-0">Avg Votes</p>
                                <small class="text-muted">Per submission</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center p-3 border-top">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 50px; height: 50px;">
                                    <i class="bi bi-arrow-up-right text-success fs-5"></i>
                                </div>
                                <h5 class="mb-1">{{ number_format($statistics['total_vote_changes'] ?? 0, 0, '.', ',') }}</h5>
                                <p class="text-muted mb-0">Total Vote Changes</p>
                                <small class="text-muted">Net vote difference</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quality Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>
                        Data Quality Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-semibold">Verification Rate</span>
                                    <span class="small">
                                        {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                    </span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" 
                                         style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ number_format($statistics['verified_submissions']) }} of {{ number_format($statistics['total_submissions']) }} verified</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-semibold">Flag Rate</span>
                                    <span class="small">
                                        {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                    </span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" 
                                         style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ number_format($statistics['flagged_submissions']) }} submissions flagged</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-semibold">Mobile App Usage</span>
                                    <span class="small">
                                        {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                    </span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" 
                                         style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ number_format($statistics['api_submissions']) }} mobile submissions</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-download me-2"></i>
                        Export & Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="{{ route('admin.audit.export') }}?start_date={{ $startDate->format('Y-m-d') }}&end_date={{ $endDate->format('Y-m-d') }}" 
                           class="btn btn-outline-primary">
                            <i class="bi bi-file-earmark-excel me-1"></i>
                            Export to Excel
                        </a>
                        <a href="{{ route('admin.audit.flagged') }}" class="btn btn-outline-warning">
                            <i class="bi bi-flag me-1"></i>
                            Review Flagged ({{ $statistics['flagged_submissions'] }})
                        </a>
                        <a href="{{ route('admin.audit.activity') }}" class="btn btn-outline-success">
                            <i class="bi bi-activity me-1"></i>
                            Live Activity Monitor
                        </a>
                        <button class="btn btn-outline-info" onclick="window.print()">
                            <i class="bi bi-printer me-1"></i>
                            Print Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style>
@endsection

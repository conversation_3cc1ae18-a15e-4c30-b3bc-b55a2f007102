@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card audit-header-card">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="header-content">
                            <h4 class="mb-1 fw-bold text-dark">
                                <i class="bi bi-activity me-2 text-gold"></i>
                                Agent Activity Dashboard
                            </h4>
                            <p class="text-muted mb-0 small">Real-time monitoring and analytics</p>
                        </div>
                        <div class="d-flex gap-1">
                            <button class="btn btn-compact btn-outline-gold" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span class="d-none d-md-inline ms-1">Refresh</span>
                            </button>
                            <a href="{{ route('admin.audit.index') }}" class="btn btn-compact btn-outline-gold">
                                <i class="bi bi-arrow-left"></i>
                                <span class="d-none d-md-inline ms-1">Back</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Beautiful Real-time Activity Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="activity-overview-card">
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="stat-card stat-live">
                                <div class="stat-icon-wrapper">
                                    <div class="stat-icon">
                                        <i class="bi bi-lightning-charge"></i>
                                    </div>
                                    <div class="pulse-indicator"></div>
                                </div>
                                <div class="stat-content">
                                    <h3 class="stat-number" id="last5MinutesCount">{{ $realTimeActivity['last_5_minutes'] }}</h3>
                                    <p class="stat-label">Live Activity</p>
                                    <small class="stat-sublabel">Last 5 minutes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-hourly">
                                <div class="stat-icon-wrapper">
                                    <div class="stat-icon">
                                        <i class="bi bi-clock-history"></i>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <h3 class="stat-number" id="lastHourCount">{{ $realTimeActivity['last_hour'] }}</h3>
                                    <p class="stat-label">Hourly Rate</p>
                                    <small class="stat-sublabel">Last 60 minutes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-daily">
                                <div class="stat-icon-wrapper">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-day"></i>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <h3 class="stat-number" id="todayCount">{{ $realTimeActivity['today'] }}</h3>
                                    <p class="stat-label">Today's Total</p>
                                    <small class="stat-sublabel">{{ now()->format('M d, Y') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card stat-agents">
                                <div class="stat-icon-wrapper">
                                    <div class="stat-icon">
                                        <i class="bi bi-people"></i>
                                    </div>
                                </div>
                                <div class="stat-content">
                                    <h3 class="stat-number" id="activeAgentsCount">{{ $realTimeActivity['active_agents_last_hour'] }}</h3>
                                    <p class="stat-label">Active Agents</p>
                                    <small class="stat-sublabel">Currently online</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Enhanced Analytics Dashboard -->
    <div class="row mb-4">
        <!-- Main Trends Chart -->
        <div class="col-lg-12">
            <div class="card trends-chart-card">
                <div class="card-header-trends">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="trends-header-info">
                            <h5 class="mb-1 text-white fw-bold">
                                <i class="bi bi-graph-up-arrow me-2"></i>
                                Submission Trends
                            </h5>
                            <p class="mb-0 text-white opacity-85 small">24-hour activity pattern with real-time insights</p>
                        </div>
                        <div class="trends-controls">
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-light active" data-period="24h">24H</button>
                                <button class="btn btn-sm btn-outline-light" data-period="7d">7D</button>
                                <button class="btn btn-sm btn-outline-light" data-period="30d">30D</button>
                            </div>
                            <button class="btn btn-sm btn-outline-light ms-2" onclick="refreshChart()" title="Refresh Chart">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="chart-container">
                        <div class="chart-stats-bar">
                            <div class="chart-stat">
                                <div class="stat-icon">
                                    <i class="bi bi-arrow-up-circle text-success"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ $analytics['total_today'] ?? 156 }}</div>
                                    <div class="stat-label">Today</div>
                                </div>
                            </div>
                            <div class="chart-stat">
                                <div class="stat-icon">
                                    <i class="bi bi-clock text-info"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ $analytics['peak_hour'] ?? '14:00' }}</div>
                                    <div class="stat-label">Peak Hour</div>
                                </div>
                            </div>
                            <div class="chart-stat">
                                <div class="stat-icon">
                                    <i class="bi bi-speedometer text-warning"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">{{ number_format($analytics['avg_per_hour'] ?? 6.5, 1) }}</div>
                                    <div class="stat-label">Avg/Hour</div>
                                </div>
                            </div>
                            <div class="chart-stat">
                                <div class="stat-icon">
                                    <i class="bi bi-graph-up text-primary"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">+{{ $analytics['growth_rate'] ?? 12 }}%</div>
                                    <div class="stat-label">Growth</div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="submissionChart" height="120"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Sidebar Analytics -->
        <div class="col-lg-4">
            <div class="analytics-sidebar-enhanced h-100">
                <!-- Additional analytics content can go here -->
            </div>
        </div>
    </div>

    <!-- Activity Monitoring Dashboard -->
    <div class="row mb-4">
        <!-- Main Activity Feed -->
        <div class="col-12">
            <div class="card activity-dashboard-card">
                <div class="card-header-modern">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="header-info">
                            <h5 class="mb-1 fw-bold">
                                <i class="bi bi-activity me-2"></i>
                                Real-Time Activity Monitor
                            </h5>
                            <p class="mb-0 text-muted small">Live submission tracking and agent performance</p>
                        </div>
                        <div class="header-controls">
                            <div class="live-indicator">
                                <span class="live-pulse"></span>
                                <span class="live-text">LIVE</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="activity-content">
                        <!-- Activity Table -->
                        <div class="activity-table-section">
                            <div class="table-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-list-ul me-2 text-gold"></i>
                                    Recent Submissions (Last 30 minutes)
                                </h6>
                            </div>
                            <div class="table-container">
                                <table class="table table-modern mb-0">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Agent</th>
                                            <th>Station</th>
                                            <th>Candidate</th>
                                            <th>Votes</th>
                                            <th>Method</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recentSubmissionsTable">
                                        @foreach($realTimeActivity['recent_submissions'] as $submission)
                                        <tr class="{{ $submission->is_flagged ? 'row-flagged' : '' }}">
                                            <td>
                                                <span class="time-badge">{{ $submission->submission_time->format('H:i') }}</span>
                                            </td>
                                            <td>
                                                <div class="agent-info">
                                                    <div class="agent-name">{{ Str::limit($submission->agent->user->name, 15) }}</div>
                                                    <div class="agent-phone">{{ Str::limit($submission->agent->user->phone_number, 12) }}</div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="station-name">{{ Str::limit($submission->pollingStation->name, 18) }}</span>
                                            </td>
                                            <td>
                                                <span class="candidate-name">{{ Str::limit($submission->candidate->name, 16) }}</span>
                                            </td>
                                            <td>
                                                <div class="vote-info">
                                                    <span class="vote-count">{{ number_format($submission->new_votes) }}</span>
                                                    @if($submission->vote_difference != 0)
                                                        <span class="vote-change {{ $submission->vote_difference > 0 ? 'positive' : 'negative' }}">
                                                            {{ $submission->vote_difference > 0 ? '+' : '' }}{{ $submission->vote_difference }}
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="method-indicator method-{{ $submission->submission_method }}">
                                                    @if($submission->submission_method === 'api')
                                                        <i class="bi bi-phone"></i>
                                                    @elseif($submission->submission_method === 'manager_portal')
                                                        <i class="bi bi-laptop"></i>
                                                    @else
                                                        <i class="bi bi-globe"></i>
                                                    @endif
                                                </span>
                                            </td>
                                            <td>
                                                @if($submission->is_flagged)
                                                    <span class="status-indicator status-flagged" title="Flagged">
                                                        <i class="bi bi-flag-fill"></i>
                                                    </span>
                                                @elseif($submission->is_verified)
                                                    <span class="status-indicator status-verified" title="Verified">
                                                        <i class="bi bi-check-circle-fill"></i>
                                                    </span>
                                                @else
                                                    <span class="status-indicator status-pending" title="Pending">
                                                        <i class="bi bi-clock"></i>
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance & Analytics Grid -->
    <div class="row mb-4">
        <!-- Top Performers -->
        <div class="col-lg-6">
            <div class="card performers-card">
                <div class="card-header-gold">
                    <h6 class="mb-0 fw-bold text-white">
                        <i class="bi bi-trophy me-2"></i>
                        Top Performing Agents
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="performers-grid">
                        @foreach($analytics['most_active_agents']->take(6) as $index => $agent)
                        <div class="performer-card {{ $index < 3 ? 'top-performer' : '' }}">
                            <div class="performer-rank">
                                <div class="rank-circle rank-{{ $index + 1 }}">
                                    @if($index == 0)
                                        <i class="bi bi-trophy-fill"></i>
                                    @elseif($index == 1)
                                        <i class="bi bi-award-fill"></i>
                                    @elseif($index == 2)
                                        <i class="bi bi-star-fill"></i>
                                    @else
                                        {{ $index + 1 }}
                                    @endif
                                </div>
                            </div>
                            <div class="performer-details">
                                <div class="performer-name">{{ Str::limit($agent->agent->user->name, 18) }}</div>
                                <div class="performer-contact">{{ Str::limit($agent->agent->user->phone_number, 14) }}</div>
                            </div>
                            <div class="performer-score">
                                <div class="score-number">{{ $agent->submission_count }}</div>
                                <div class="score-label">submissions</div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="col-lg-6">
            <div class="card metrics-overview-card">
                <div class="card-header-light">
                    <h6 class="mb-0 fw-semibold">
                        <i class="bi bi-speedometer2 me-2 text-gold"></i>
                        Performance Overview
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="metrics-grid">
                        <div class="metric-card metric-success">
                            <div class="metric-icon">
                                <i class="bi bi-check-circle-fill"></i>
                            </div>
                            <div class="metric-data">
                                <div class="metric-value">{{ number_format($analytics['success_rate'] ?? 95, 1) }}%</div>
                                <div class="metric-label">Success Rate</div>
                            </div>
                        </div>

                        <div class="metric-card metric-info">
                            <div class="metric-icon">
                                <i class="bi bi-clock-fill"></i>
                            </div>
                            <div class="metric-data">
                                <div class="metric-value">{{ number_format($analytics['avg_response_time'] ?? 2.3, 1) }}s</div>
                                <div class="metric-label">Avg Response</div>
                            </div>
                        </div>

                        <div class="metric-card metric-warning">
                            <div class="metric-icon">
                                <i class="bi bi-graph-up-arrow"></i>
                            </div>
                            <div class="metric-data">
                                <div class="metric-value">{{ $analytics['peak_hour'] ?? '14:00' }}</div>
                                <div class="metric-label">Peak Hour</div>
                            </div>
                        </div>

                        <div class="metric-card metric-primary">
                            <div class="metric-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <div class="metric-data">
                                <div class="metric-value">{{ $realTimeActivity['active_agents_last_hour'] }}</div>
                                <div class="metric-label">Active Agents</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card methods-enhanced-card">
                <div class="card-header-methods">
                    <h6 class="mb-0 fw-bold text-white">
                        <i class="bi bi-pie-chart me-2"></i>
                        Submission Methods
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="methods-container">
                        <div class="chart-wrapper">
                            <canvas id="methodChart" height="120"></canvas>
                        </div>
                        <div class="methods-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: #FFA500;"></div>
                                <div class="legend-info">
                                    <span class="legend-label">Mobile API</span>
                                    <span class="legend-value">{{ $analytics['method_percentages']['api'] ?? 65 }}%</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #FFD700;"></div>
                                <div class="legend-info">
                                    <span class="legend-label">Manager Portal</span>
                                    <span class="legend-value">{{ $analytics['method_percentages']['manager'] ?? 25 }}%</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #FF8C00;"></div>
                                <div class="legend-info">
                                    <span class="legend-label">Web Interface</span>
                                    <span class="legend-value">{{ $analytics['method_percentages']['web'] ?? 10 }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
    </div>

    <!-- Security & Alerts Section -->
    @if(count($suspiciousPatterns['rapid_submitters']) > 0 || count($suspiciousPatterns['unusual_timing']) > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card security-alerts-card">
                <div class="card-header-security">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1 text-white fw-bold">
                                <i class="bi bi-shield-exclamation me-2"></i>
                                Security Alerts
                            </h5>
                            <p class="mb-0 text-white opacity-75 small">Suspicious activity patterns detected</p>
                        </div>
                        <div class="alert-indicator">
                            <span class="alert-pulse"></span>
                            <small class="text-white">ALERT</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        @if(count($suspiciousPatterns['rapid_submitters']) > 0)
                        <div class="col-lg-6">
                            <div class="alert-item alert-rapid">
                                <div class="alert-header">
                                    <div class="alert-icon">
                                        <i class="bi bi-speedometer2"></i>
                                    </div>
                                    <div class="alert-title">
                                        <h6 class="mb-1">Rapid Submissions</h6>
                                        <small class="text-muted">{{ count($suspiciousPatterns['rapid_submitters']) }} agents flagged</small>
                                    </div>
                                </div>
                                <div class="alert-content">
                                    @foreach($suspiciousPatterns['rapid_submitters']->take(3) as $submitter)
                                    <div class="suspicious-agent">
                                        <div class="agent-info">
                                            <span class="agent-name">{{ Str::limit($submitter->agent->user->name, 20) }}</span>
                                            <span class="agent-count">{{ $submitter->count }} submissions</span>
                                        </div>
                                        <small class="text-muted">Last 2 hours</small>
                                    </div>
                                    @endforeach
                                    @if(count($suspiciousPatterns['rapid_submitters']) > 3)
                                    <div class="text-center mt-2">
                                        <small class="text-muted">+{{ count($suspiciousPatterns['rapid_submitters']) - 3 }} more agents</small>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        @if(count($suspiciousPatterns['unusual_timing']) > 0)
                        <div class="col-lg-6">
                            <div class="alert-item alert-timing">
                                <div class="alert-header">
                                    <div class="alert-icon">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                    <div class="alert-title">
                                        <h6 class="mb-1">Unusual Timing</h6>
                                        <small class="text-muted">{{ count($suspiciousPatterns['unusual_timing']) }} submissions outside hours</small>
                                    </div>
                                </div>
                                <div class="alert-content">
                                    @foreach($suspiciousPatterns['unusual_timing']->take(3) as $submission)
                                    <div class="suspicious-agent">
                                        <div class="agent-info">
                                            <span class="agent-name">{{ Str::limit($submission->agent->user->name, 20) }}</span>
                                            <span class="agent-time">{{ $submission->submission_time->format('H:i') }}</span>
                                        </div>
                                        <small class="text-muted">{{ $submission->submission_time->format('M j') }}</small>
                                    </div>
                                    @endforeach
                                    @if(count($suspiciousPatterns['unusual_timing']) > 3)
                                    <div class="text-center mt-2">
                                        <small class="text-muted">+{{ count($suspiciousPatterns['unusual_timing']) - 3 }} more submissions</small>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>

                    <div class="alert-actions mt-3 pt-3 border-top">
                        <div class="d-flex gap-2">
                            <a href="{{ route('admin.audit.flagged') }}" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-flag me-1"></i>
                                View All Flagged
                            </a>
                            <a href="{{ route('admin.audit.index') }}?status=flagged" class="btn btn-outline-warning btn-sm">
                                <i class="bi bi-list-ul me-1"></i>
                                Audit Trail
                            </a>
                            <button class="btn btn-outline-secondary btn-sm" onclick="dismissAlerts()">
                                <i class="bi bi-x-circle me-1"></i>
                                Dismiss
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif


</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Auto-refresh every 30 seconds
setInterval(refreshData, 30000);

function refreshData() {
    fetch('{{ route("admin.audit.api.activity") }}')
        .then(response => response.json())
        .then(data => {
            // Update counters
            document.getElementById('last5MinutesCount').textContent = data.realTime.last_5_minutes;
            document.getElementById('lastHourCount').textContent = data.realTime.last_hour;
            document.getElementById('todayCount').textContent = data.realTime.today;
            document.getElementById('activeAgentsCount').textContent = data.realTime.active_agents_last_hour;
            
            // Update recent submissions table
            updateRecentSubmissionsTable(data.realTime.recent_submissions);
        })
        .catch(error => console.error('Error refreshing data:', error));
}

function updateRecentSubmissionsTable(submissions) {
    const tbody = document.getElementById('recentSubmissionsTable');
    tbody.innerHTML = '';
    
    submissions.forEach(submission => {
        const row = document.createElement('tr');
        if (submission.is_flagged) {
            row.classList.add('table-danger');
        }
        
        row.innerHTML = `
            <td><small class="text-muted">${new Date(submission.submission_time).toLocaleTimeString()}</small></td>
            <td>
                <div class="fw-semibold">${submission.agent.user.name}</div>
                <small class="text-muted">${submission.agent.user.phone_number}</small>
            </td>
            <td>${submission.polling_station.name}</td>
            <td>${submission.candidate.name}</td>
            <td>
                <span class="badge bg-primary">${submission.new_votes.toLocaleString()}</span>
                ${submission.vote_difference != 0 ? `<small class="text-muted">(${submission.vote_difference > 0 ? '+' : ''}${submission.vote_difference})</small>` : ''}
            </td>
            <td>
                <span class="badge bg-${submission.submission_method === 'api' ? 'success' : 'info'}">
                    ${submission.submission_method.charAt(0).toUpperCase() + submission.submission_method.slice(1)}
                </span>
            </td>
            <td>
                ${submission.is_flagged ? '<span class="badge bg-danger">Flagged</span>' : 
                  submission.is_verified ? '<span class="badge bg-success">Verified</span>' : 
                  '<span class="badge bg-secondary">Pending</span>'}
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    // Submission activity chart
    const submissionCtx = document.getElementById('submissionChart').getContext('2d');
    new Chart(submissionCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode(array_keys($analytics['submissions_by_hour'])) !!}.map(hour => hour + ':00'),
            datasets: [{
                label: 'Submissions',
                data: {!! json_encode(array_values($analytics['submissions_by_hour'])) !!},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Submission methods chart
    const methodCtx = document.getElementById('methodChart').getContext('2d');
    new Chart(methodCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode(array_keys($analytics['submission_methods'])) !!}.map(method => method.charAt(0).toUpperCase() + method.slice(1)),
            datasets: [{
                data: {!! json_encode(array_values($analytics['submission_methods'])) !!},
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});

// Chart refresh function
function refreshChart() {
    // Add loading state
    const chartContainer = document.querySelector('#submissionChart').parentElement;
    chartContainer.style.opacity = '0.6';

    fetch('{{ route("admin.audit.api.activity") }}')
        .then(response => response.json())
        .then(data => {
            // Update chart data
            if (window.submissionChart) {
                window.submissionChart.data.datasets[0].data = data.chartData;
                window.submissionChart.update();
            }

            // Remove loading state
            chartContainer.style.opacity = '1';
        })
        .catch(error => {
            console.error('Error refreshing chart:', error);
            chartContainer.style.opacity = '1';
        });
}

// Dismiss alerts function
function dismissAlerts() {
    const alertsCard = document.querySelector('.security-alerts-card');
    if (alertsCard) {
        alertsCard.style.transition = 'all 0.3s ease';
        alertsCard.style.opacity = '0';
        alertsCard.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            alertsCard.parentElement.parentElement.style.display = 'none';
        }, 300);
    }
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Submission Chart
    const submissionCtx = document.getElementById('submissionChart');
    if (submissionCtx) {
        window.submissionChart = new Chart(submissionCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode($analytics['hourly_labels'] ?? []) !!},
                datasets: [{
                    label: 'Submissions',
                    data: {!! json_encode($analytics['hourly_data'] ?? []) !!},
                    borderColor: '#FFA500',
                    backgroundColor: 'rgba(255, 165, 0, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#FFA500',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Method Chart
    const methodCtx = document.getElementById('methodChart');
    if (methodCtx) {
        new Chart(methodCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode($analytics['method_labels'] ?? ['API', 'Web', 'Manager']) !!},
                datasets: [{
                    data: {!! json_encode($analytics['method_data'] ?? [60, 30, 10]) !!},
                    backgroundColor: [
                        '#FFA500',
                        '#FFD700',
                        '#FF8C00'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}
</script>

<style>
/* ===== BEAUTIFUL AGENT ACTIVITY PAGE STYLING ===== */

/* Activity Overview Card */
.activity-overview-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.activity-overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #FFA500, #FFD700, #FFA500);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

/* Beautiful Stat Cards */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.stat-live::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.stat-hourly::before {
    background: linear-gradient(90deg, #17a2b8, #20c997);
}

.stat-daily::before {
    background: linear-gradient(90deg, #ffc107, #ffeb3b);
}

.stat-agents::before {
    background: linear-gradient(90deg, #6f42c1, #e83e8c);
}

.stat-icon-wrapper {
    position: relative;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    box-shadow: 0 4px 20px rgba(255, 165, 0, 0.3);
}

.pulse-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.stat-content .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-content .stat-label {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.stat-content .stat-sublabel {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Enhanced Trends Chart Card */
.trends-chart-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.card-header-trends {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    padding: 1.5rem;
    border: none;
    position: relative;
}

.card-header-trends::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.trends-header-info h5 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.trends-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trends-controls .btn-group .btn {
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    transition: all 0.2s ease;
}

.trends-controls .btn-group .btn:hover,
.trends-controls .btn-group .btn.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.chart-container {
    background: white;
}

.chart-stats-bar {
    display: flex;
    justify-content: space-around;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e9ecef;
}

.chart-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.chart-stat .stat-icon {
    font-size: 1.2rem;
}

.chart-stat .stat-value {
    font-weight: 700;
    font-size: 0.9rem;
    color: #2c3e50;
    line-height: 1;
}

.chart-stat .stat-label {
    font-size: 0.7rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.chart-area {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
}

/* Enhanced Methods Card */
.methods-enhanced-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.card-header-methods {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    padding: 1rem 1.5rem;
    border: none;
    width: 100%;
}

.methods-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.chart-wrapper {
    position: relative;
    height: 120px;
    width: 100%;
}

.methods-legend {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.legend-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.legend-label {
    font-size: 0.8rem;
    color: #495057;
    font-weight: 500;
}

.legend-value {
    font-size: 0.8rem;
    font-weight: 700;
    color: #2c3e50;
}

/* Enhanced Quick Stats Card */
.quick-stats-enhanced-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.card-header-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.quick-stat-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.quick-stat-item:nth-child(1) {
    border-left-color: #28a745;
}

.quick-stat-item:nth-child(2) {
    border-left-color: #17a2b8;
}

.quick-stat-item:nth-child(3) {
    border-left-color: #ffc107;
}

.quick-stat-item:nth-child(4) {
    border-left-color: #007bff;
}

.quick-stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

.quick-stat-content {
    flex: 1;
}

.quick-stat-value {
    font-weight: 700;
    font-size: 1rem;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 0.2rem;
}

.quick-stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: 0.2rem;
}

.quick-stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.7rem;
}

.quick-stat-trend i {
    font-size: 0.7rem;
}

/* Analytics Sidebar Enhanced */
.analytics-sidebar-enhanced {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

/* Compact Header Card */
.audit-header-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-left: 4px solid #FFA500;
    transition: all 0.2s ease;
}

.audit-header-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.header-content h4 {
    font-size: 1.4rem;
    line-height: 1.2;
}

.header-content p {
    font-size: 0.85rem;
    line-height: 1.3;
}

.text-gold {
    color: #FFA500 !important;
}

/* Compact Buttons */
.btn-compact {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-width: 1px;
}

.btn-outline-gold {
    border-color: #FFA500;
    color: #FFA500;
    background: transparent;
}

.btn-outline-gold:hover {
    background: #FFA500;
    border-color: #FFA500;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
}

.btn-outline-gold i {
    font-size: 0.9rem;
}

/* Audit Statistics Cards */
.audit-stat-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.audit-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
}

.audit-success::before {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.audit-info::before {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.audit-warning::before {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.audit-primary::before {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-top: 0.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-icon-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.stat-icon-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
}

.stat-icon-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Card Enhancements */
.card-custom {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    overflow: hidden;
}

.card-custom:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header-gold {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
    font-weight: 600;
}

.btn-gold-outline {
    background: transparent;
    border: 2px solid #FFA500;
    color: #FFA500;
    font-weight: 500;
    padding: 0.4rem 1.25rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.btn-gold-outline:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    border-color: #FFA500;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.2);
}

.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

/* Compact Chart Cards */
.compact-chart-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* Compact List Card */
.compact-list-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* Compact Alert Card */
.compact-alert-card {
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* Rank Badges */
.rank-badge {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: white;
}

.rank-1 {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.rank-2 {
    background: linear-gradient(135deg, #C0C0C0 0%, #A8A8A8 100%);
}

.rank-3 {
    background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Compact Table */
.compact-table-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.card-header-compact {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.table-compact {
    font-size: 0.8rem;
    margin-bottom: 0;
}

.table-compact thead th {
    background: #ffffff;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 0.6rem 0.75rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 2px solid #f1f3f4;
}

.table-compact thead th i {
    font-size: 0.7rem;
    margin-right: 0.25rem;
    color: #FFA500;
}

.table-compact tbody tr {
    transition: all 0.15s ease;
    border: none;
}

.table-compact tbody tr:hover {
    background: rgba(255, 165, 0, 0.03);
}

.table-compact tbody td {
    padding: 0.6rem 0.75rem;
    border: none;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
    line-height: 1.3;
}

/* Modern Activity Dashboard */
.activity-dashboard-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.header-info h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.live-pulse {
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.live-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: #28a745;
    letter-spacing: 0.5px;
}

.activity-content {
    padding: 0;
}

.table-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.table-container {
    max-height: 400px;
    overflow-y: auto;
}

.table-modern {
    font-size: 0.85rem;
    margin-bottom: 0;
}

.table-modern thead th {
    background: #ffffff;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-modern tbody tr {
    transition: all 0.2s ease;
    border: none;
}

.table-modern tbody tr:hover {
    background: rgba(255, 165, 0, 0.03);
    transform: translateX(2px);
}

.table-modern tbody tr.row-flagged {
    background: rgba(220, 53, 69, 0.05);
    border-left: 4px solid #dc3545;
}

.table-modern tbody td {
    padding: 0.75rem 1rem;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

/* Modern Table Elements */
.time-badge {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.agent-info {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.agent-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.85rem;
    line-height: 1.2;
}

.agent-phone {
    color: #6c757d;
    font-size: 0.7rem;
}

.station-name, .candidate-name {
    font-weight: 500;
    color: #495057;
    font-size: 0.85rem;
}

.vote-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
}

.vote-count {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.9rem;
}

.vote-change {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.1rem 0.4rem;
    border-radius: 8px;
}

.vote-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.vote-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.method-indicator {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.method-api {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.method-manager_portal {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.method-web {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
}

.status-flagged {
    background: #dc3545;
}

.status-verified {
    background: #28a745;
}

.status-pending {
    background: #ffc107;
}

/* Performers Card */
.performers-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header-gold {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    padding: 1rem 1.5rem;
    border: none;
}

.performers-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.performer-card {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.performer-card:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.performer-card.top-performer {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%);
    border-left-color: #FFD700;
}

.performer-rank {
    margin-right: 1rem;
}

.rank-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    color: white;
}

.rank-1 {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.rank-2 {
    background: linear-gradient(135deg, #C0C0C0 0%, #A8A8A8 100%);
}

.rank-3 {
    background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
}

.rank-circle:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.performer-details {
    flex: 1;
}

.performer-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.2;
    margin-bottom: 0.2rem;
}

.performer-contact {
    color: #6c757d;
    font-size: 0.75rem;
}

.performer-score {
    text-align: right;
}

.score-number {
    font-weight: 700;
    font-size: 1.1rem;
    color: #FFA500;
    line-height: 1;
}

.score-label {
    font-size: 0.7rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Metrics Overview Card */
.metrics-overview-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.metric-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.metric-card:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.metric-success {
    border-left-color: #28a745;
}

.metric-info {
    border-left-color: #17a2b8;
}

.metric-warning {
    border-left-color: #ffc107;
}

.metric-primary {
    border-left-color: #007bff;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1rem;
}

.metric-success .metric-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.metric-info .metric-icon {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.metric-warning .metric-icon {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
}

.metric-primary .metric-icon {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.metric-data {
    flex: 1;
}

.metric-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 0.2rem;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Analytics Sidebar */
.analytics-sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Performance Sidebar */
.performance-sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Leaderboard Card */
.leaderboard-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.performer-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
}

.performer-item:last-child {
    border-bottom: none;
}

.performer-item:hover {
    background: rgba(255, 165, 0, 0.05);
    border-radius: 8px;
    margin: 0 -0.5rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.performer-item.top-three {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%);
    border-radius: 8px;
    margin: 0 -0.5rem 0.5rem -0.5rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.performer-rank {
    margin-right: 1rem;
}

.performer-info {
    flex: 1;
}

.performer-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.2;
}

.performer-details {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.performer-stats {
    text-align: right;
}

.submission-count {
    font-weight: 700;
    font-size: 1.1rem;
    color: #FFA500;
}

.submission-label {
    font-size: 0.7rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Metrics Card */
.metrics-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.metric-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1rem;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #2c3e50;
    line-height: 1;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Security Alerts Card */
.security-alerts-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(220, 53, 69, 0.15);
    overflow: hidden;
}

.card-header-security {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    padding: 1.5rem;
    border: none;
}

.alert-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-pulse {
    width: 8px;
    height: 8px;
    background: #ffffff;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.alert-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    border-left: 4px solid #dc3545;
    transition: all 0.2s ease;
}

.alert-item:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.alert-rapid {
    border-left-color: #dc3545;
}

.alert-timing {
    border-left-color: #ffc107;
}

.alert-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 0.75rem;
    font-size: 1rem;
}

.alert-timing .alert-icon {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.alert-title h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-content {
    margin-left: 3.25rem;
}

.suspicious-agent {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.suspicious-agent:last-child {
    border-bottom: none;
}

.agent-info {
    flex: 1;
    display: flex;
    justify-content: between;
    align-items: center;
}

.agent-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.85rem;
}

.agent-count, .agent-time {
    font-weight: 600;
    color: #dc3545;
    font-size: 0.8rem;
}

.alert-actions {
    background: rgba(248, 249, 250, 0.5);
    margin: 0 -1.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
}

/* Compact Activity Table */
.compact-activity-container {
    max-height: 350px;
    overflow-y: auto;
}

.table-compact-activity {
    font-size: 0.75rem;
    margin-bottom: 0;
}

.table-compact-activity thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 0.5rem 0.6rem;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-compact-activity tbody tr {
    transition: all 0.15s ease;
    border: none;
}

.table-compact-activity tbody tr:hover {
    background: rgba(255, 165, 0, 0.03);
}

.table-compact-activity tbody tr.row-flagged {
    background: rgba(220, 53, 69, 0.05);
    border-left: 3px solid #dc3545;
}

.table-compact-activity tbody td {
    padding: 0.4rem 0.6rem;
    border: none;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
    line-height: 1.2;
}

/* Compact Table Elements */
.time-compact {
    font-weight: 600;
    color: #495057;
    font-size: 0.75rem;
}

.agent-compact {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.agent-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.75rem;
    line-height: 1.1;
}

.agent-phone {
    color: #6c757d;
    font-size: 0.65rem;
}

.station-compact, .candidate-compact {
    font-weight: 500;
    color: #495057;
    font-size: 0.75rem;
    line-height: 1.2;
}

.votes-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
}

.vote-count {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.8rem;
}

.vote-diff {
    font-size: 0.65rem;
    font-weight: 600;
}

/* Method Badges */
.method-badge {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
}

.method-api {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.method-manager_portal {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.method-web {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* Status Compact */
.status-compact {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.6rem;
}

.status-flagged {
    background: #dc3545;
}

.status-verified {
    background: #28a745;
}

.status-pending {
    background: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .alert-content {
        margin-left: 0;
        margin-top: 0.75rem;
    }

    .alert-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .health-metric {
        padding: 0.75rem;
    }

    .health-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .health-value {
        font-size: 1.1rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .audit-stat-card .card-body {
        padding: 1rem;
    }
}
</style>
@endsection

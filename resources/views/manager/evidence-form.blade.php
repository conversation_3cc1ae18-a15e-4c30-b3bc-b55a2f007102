@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-upload text-success me-2"></i>
                        Upload Evidence
                    </h2>
                    <p class="text-muted mb-0">{{ $station->name }} - {{ $station->district }}</p>
                </div>
                <a href="{{ route('manager.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Station Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Station Information</h6>
                    <table class="table table-sm table-borderless">
                        <tr><td><strong>Name:</strong></td><td>{{ $station->name }}</td></tr>
                        <tr><td><strong>District:</strong></td><td>{{ $station->district }}</td></tr>
                        <tr><td><strong>County:</strong></td><td>{{ $station->county }}</td></tr>
                        <tr><td><strong>Subcounty:</strong></td><td>{{ $station->subcounty }}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">Agent Information</h6>
                    @if($agent && $agent->user)
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Name:</strong></td><td>{{ $agent->user->name }}</td></tr>
                            <tr><td><strong>Phone:</strong></td><td>{{ $agent->user->phone_number }}</td></tr>
                        </table>
                    @else
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            No agent assigned to this station
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if(!$agent)
        <div class="alert alert-danger">
            <h6><i class="bi bi-x-circle me-2"></i>Cannot Upload Evidence</h6>
            <p class="mb-0">This polling station does not have an agent assigned. Please assign an agent before uploading evidence.</p>
        </div>
    @else
        <!-- Existing Evidence -->
        @if($existingEvidence && $existingEvidence->count() > 0)
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-files me-2"></i>
                    Existing Evidence ({{ $existingEvidence->count() }} files)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($existingEvidence as $evidence)
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="evidence-item">
                            <div class="d-flex align-items-center">
                                <div class="evidence-icon me-3">
                                    @if(in_array(pathinfo($evidence->file_url, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png']))
                                        <i class="bi bi-image text-primary"></i>
                                    @else
                                        <i class="bi bi-file-earmark text-secondary"></i>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-semibold">{{ $evidence->file_name ?: 'Evidence File' }}</div>
                                    <small class="text-muted">{{ $evidence->created_at->format('M d, Y H:i') }}</small>
                                    <div class="mt-1">
                                        <a href="{{ asset('files/' . $evidence->file_url) }}" 
                                           target="_blank" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>View
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Evidence Upload Form -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-cloud-upload me-2"></i>
                    Upload New Evidence
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('manager.upload-evidence', $station) }}" method="POST" enctype="multipart/form-data" id="evidenceForm">
                    @csrf
                    
                    <div id="fileInputs">
                        <div class="file-input-group mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Evidence File</label>
                                    <input type="file" 
                                           class="form-control" 
                                           name="evidence_files[]" 
                                           accept=".jpg,.jpeg,.png,.pdf"
                                           required>
                                    <div class="form-text">Accepted formats: JPG, PNG, PDF (Max 5MB)</div>
                                </div>
                                <div class="col-md-5">
                                    <label class="form-label">File Description</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="file_names[]" 
                                           placeholder="e.g., DR Form, Tally Sheet, etc.">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" 
                                            class="btn btn-outline-danger remove-file" 
                                            onclick="removeFileInput(this)"
                                            style="display: none;">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-secondary" onclick="addFileInput()">
                            <i class="bi bi-plus-circle me-1"></i>
                            Add Another File
                        </button>
                    </div>

                    <hr>

                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg px-5">
                            <i class="bi bi-cloud-upload me-2"></i>
                            Upload Evidence
                        </button>
                        <div class="mt-2">
                            <small class="text-muted">
                                Files will be uploaded for {{ $station->name }}
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endif
</div>

<style>
.evidence-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.evidence-item:hover {
    border-color: #0d6efd;
    background: #fff;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.evidence-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.file-input-group {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}
</style>

<script>
function addFileInput() {
    const container = document.getElementById('fileInputs');
    const newGroup = document.createElement('div');
    newGroup.className = 'file-input-group mb-3';
    newGroup.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">Evidence File</label>
                <input type="file" 
                       class="form-control" 
                       name="evidence_files[]" 
                       accept=".jpg,.jpeg,.png,.pdf"
                       required>
                <div class="form-text">Accepted formats: JPG, PNG, PDF (Max 5MB)</div>
            </div>
            <div class="col-md-5">
                <label class="form-label">File Description</label>
                <input type="text" 
                       class="form-control" 
                       name="file_names[]" 
                       placeholder="e.g., DR Form, Tally Sheet, etc.">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" 
                        class="btn btn-outline-danger remove-file" 
                        onclick="removeFileInput(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newGroup);
    updateRemoveButtons();
}

function removeFileInput(button) {
    button.closest('.file-input-group').remove();
    updateRemoveButtons();
}

function updateRemoveButtons() {
    const groups = document.querySelectorAll('.file-input-group');
    groups.forEach((group, index) => {
        const removeBtn = group.querySelector('.remove-file');
        if (groups.length > 1) {
            removeBtn.style.display = 'block';
        } else {
            removeBtn.style.display = 'none';
        }
    });
}

// Form validation
document.getElementById('evidenceForm').addEventListener('submit', function(e) {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    let hasFiles = false;
    
    fileInputs.forEach(input => {
        if (input.files.length > 0) {
            hasFiles = true;
        }
    });
    
    if (!hasFiles) {
        e.preventDefault();
        alert('Please select at least one file to upload.');
        return false;
    }
    
    // Check file sizes
    let oversizedFiles = [];
    fileInputs.forEach((input, index) => {
        if (input.files.length > 0) {
            const file = input.files[0];
            if (file.size > 5 * 1024 * 1024) { // 5MB
                oversizedFiles.push(file.name);
            }
        }
    });
    
    if (oversizedFiles.length > 0) {
        e.preventDefault();
        alert('The following files are too large (max 5MB): ' + oversizedFiles.join(', '));
        return false;
    }
    
    // Confirm upload
    if (!confirm('Are you sure you want to upload these evidence files for {{ $station->name }}?')) {
        e.preventDefault();
        return false;
    }
});

// Initialize remove buttons
document.addEventListener('DOMContentLoaded', function() {
    updateRemoveButtons();
});
</script>
@endsection

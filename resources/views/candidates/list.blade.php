@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.2);
        transition: all 0.3s;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 165, 0, 0.3);
        color: white;
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .candidate-name {
        font-weight: 500;
        color: #333;
    }
    
    .candidate-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #FFA500;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }
    
    .party-badge {
        padding: 0.35rem 0.75rem;
        border-radius: 30px;
        font-weight: 500;
        font-size: 0.8rem;
        display: inline-block;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .modal-content-custom {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .modal-header-custom {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        border: none;
        padding: 1.25rem 1.5rem;
    }
    
    .modal-title-custom {
        font-weight: 600;
        font-size: 1.25rem;
    }
    
    .modal-body-custom {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    
    .form-control-custom {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        border: 1px solid #dee2e6;
        transition: all 0.2s;
    }
    
    .form-control-custom:focus {
        border-color: #FFA500;
        box-shadow: 0 0 0 0.25rem rgba(255, 165, 0, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.3);
    }
    
    .btn-cancel {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background-color: #e9ecef;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete confirmation for candidates
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this candidate?')) {
                    this.submit();
                }
            });
        });
        
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('.table-custom tbody tr');
                
                tableRows.forEach(row => {
                    const candidateName = row.querySelector('.candidate-name').textContent.toLowerCase();
                    const partyName = row.querySelector('.party-badge').textContent.toLowerCase();
                    
                    if (candidateName.includes(searchTerm) || partyName.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                // Check if any results are visible
                const visibleRows = document.querySelectorAll('.table-custom tbody tr[style=""]');
                const noResultsMessage = document.getElementById('noResultsMessage');
                
                if (visibleRows.length === 0 && searchTerm !== '') {
                    if (!noResultsMessage) {
                        const tbody = document.querySelector('.table-custom tbody');
                        const noResults = document.createElement('tr');
                        noResults.id = 'noResultsMessage';
                        noResults.innerHTML = `<td colspan="4" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-search text-muted mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0">No candidates found matching "${searchTerm}"</p>
                            </div>
                        </td>`;
                        tbody.appendChild(noResults);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            });
        }
    });
</script>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">{{ $position->name }} Candidates</h4>
            <p class="text-muted">Manage all candidates for this position</p>
        </div>
        <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_candidate">
            <i class="bi bi-plus-circle"></i> Add Candidate
        </button>
    </div>
    
    <!-- Main Content Card -->
    <div class="card card-custom">
        <div class="card-header-custom d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="bi bi-person-badge me-2 text-warning"></i>
                <span class="fw-medium">All Candidates</span>
            </div>
            <div class="input-group" style="width: 250px;">
                <input type="text" class="form-control" placeholder="Search candidates..." id="searchInput">
                <span class="input-group-text bg-light">
                    <i class="bi bi-search"></i>
                </span>
            </div>
        </div>
        <div class="card-body-custom p-0">
            @if(count($candidates) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Photo</th>
                            <th>Candidate</th>
                            <th>Party</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($candidates as $candidate)
                            <tr>
                                <td>
                                    @if(isset($candidate->picture))
                                        <img src="{{ asset('files/'.$candidate->picture) }}" alt="{{ $candidate->name }}" class="candidate-avatar">
                                    @else
                                        @php
                                            // Get candidate initials (up to 2 characters)
                                            $nameParts = explode(' ', $candidate->name);
                                            $initials = '';
                                            if (count($nameParts) >= 2) {
                                                $initials = strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1));
                                            } else {
                                                $initials = strtoupper(substr($candidate->name, 0, 2));
                                            }
                                            // Use party color if available, otherwise default color
                                            $bgColor = $candidate->party_color ? $candidate->party_color : '#6c757d';
                                        @endphp
                                        <div class="candidate-avatar d-flex align-items-center justify-content-center" 
                                             style="background: {{ $bgColor }}; color: white; font-weight: bold;">
                                            {{ $initials }}
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    <span class="candidate-name">{{ $candidate->name }}</span>
                                </td>
                                <td>
                                    <span class="party-badge" style="background-color: {{$candidate->party_color}}">
                                        {{ $candidate->party_name }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('candidates.edit',$candidate->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        <form action="{{ route('candidates.destroy',$candidate->id) }}" method="POST" class="d-inline delete-form">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-action btn-delete">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>                            
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="empty-state p-4">
                <div class="empty-state-icon">
                    <i class="bi bi-person-badge"></i>
                </div>
                <h5 class="empty-state-text">No candidates found for this position</h5>
                <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_candidate">
                    <i class="bi bi-plus-circle"></i> Add Your First Candidate
                </button>
            </div>
            @endif              
        </div>
    </div>
</div>


<div class="modal fade" id="add_candidate" tabindex="-1" aria-labelledby="candidateModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modal-content-custom">
      <div class="modal-header modal-header-custom">
        <h5 class="modal-title modal-title-custom" id="candidateModalLabel">
            <i class="bi bi-person-plus me-2"></i> Add New Candidate
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body modal-body-custom">
        <form action="{{ route('candidates.store') }}" method="post" enctype="multipart/form-data" id="candidateForm">
            @csrf
            <input type="hidden" name="position_id" value="{{ $position->id }}">
            
            <!-- Photo Upload Section -->
            <div class="text-center mb-4">
                <div class="photo-upload-container">
                    <div class="photo-preview" id="photoPreview">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="photo-upload-overlay">
                        <i class="bi bi-camera"></i>
                    </div>
                    <input type="file" name="picture" id="picture" accept="image/*" class="photo-upload-input" onchange="previewImage(this)">
                </div>
                <label for="picture" class="photo-upload-label mt-2">Upload Candidate Photo</label>
                <small class="text-muted d-block">Recommended size: 300x300 pixels</small>
            </div>
            
            <!-- Candidate Details Section -->
            <div class="form-group">
                <label for="name" class="form-label">Candidate Name <span class="text-danger">*</span></label>
                <div class="input-group mb-3">
                    <span class="input-group-text bg-light">
                        <i class="bi bi-person"></i>
                    </span>
                    <input type="text" name="name" id="name" class="form-control form-control-custom" placeholder="Enter candidate's full name" required>
                </div>
            </div>

            <!-- Party Information Section with Live Preview -->
            <div class="card mb-3 border-0 bg-light">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h6 class="mb-0"><i class="bi bi-flag me-2"></i>Party Information</h6>
                </div>
                <div class="card-body pt-2">
                    <div class="row">
                        <div class="col-md-7">
                            <div class="form-group">
                                <label for="party_name" class="form-label">Party Name <span class="text-danger">*</span></label>
                                <input type="text" name="party_name" id="party_name" class="form-control form-control-custom" placeholder="Enter party name" required oninput="updatePartyBadge()">
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="party_color" class="form-label">Party Color <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="color" name="party_color" id="party_color" class="form-control form-control-color" value="#FFA500" required oninput="updatePartyBadge()" style="height: 38px; width: 100%;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Live Party Badge Preview -->
                    <div class="mt-3 text-center">
                        <label class="form-label">Preview</label>
                        <div class="party-badge-preview p-2">
                            <span class="party-badge" id="partyBadgePreview" style="background-color: #FFA500">
                                Party Name
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-save">
                    <i class="bi bi-check-circle me-1"></i> Save Candidate
                </button>
            </div>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
    .photo-upload-container {
        position: relative;
        width: 120px;
        height: 120px;
        margin: 0 auto;
        border-radius: 50%;
        overflow: hidden;
        background-color: #f8f9fa;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 3px solid #FFD700;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .photo-upload-container:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(255, 165, 0, 0.2);
    }
    
    .photo-preview {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }
    
    .photo-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .photo-preview i {
        font-size: 3.5rem;
        color: #ccc;
    }
    
    .photo-upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
    }
    
    .photo-upload-container:hover .photo-upload-overlay {
        opacity: 1;
    }
    
    .photo-upload-overlay i {
        font-size: 2rem;
        color: white;
    }
    
    .photo-upload-input {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        opacity: 0;
        cursor: pointer;
    }
    
    .photo-upload-label {
        font-weight: 500;
        color: #495057;
        cursor: pointer;
    }
    
    .party-badge-preview {
        background-color: #f8f9fa;
        border-radius: 8px;
    }
</style>

<script>
    function previewImage(input) {
        const preview = document.getElementById('photoPreview');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                // Clear the preview
                preview.innerHTML = '';
                
                // Create image element
                const img = document.createElement('img');
                img.src = e.target.result;
                preview.appendChild(img);
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
    
    function updatePartyBadge() {
        const partyName = document.getElementById('party_name').value || 'Party Name';
        const partyColor = document.getElementById('party_color').value;
        const badgePreview = document.getElementById('partyBadgePreview');
        
        badgePreview.textContent = partyName;
        badgePreview.style.backgroundColor = partyColor;
        
        // Adjust text color based on background brightness
        const r = parseInt(partyColor.slice(1, 3), 16);
        const g = parseInt(partyColor.slice(3, 5), 16);
        const b = parseInt(partyColor.slice(5, 7), 16);
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        
        badgePreview.style.color = brightness > 125 ? '#000' : '#fff';
    }
    
    // Initialize when modal is shown
    document.addEventListener('DOMContentLoaded', function() {
        const addCandidateModal = document.getElementById('add_candidate');
        if (addCandidateModal) {
            addCandidateModal.addEventListener('shown.bs.modal', function() {
                updatePartyBadge();
            });
        }
    });
</script>
 
@endsection

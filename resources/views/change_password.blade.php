@extends('layouts.app')

@section('content')
<div class="container-fluid">

   
 
    <div class="station-card">
        <div class="station-header">
            <h5 class="station-name">
                <i class="bi bi-building"></i>
                Change Password
            </h5>           
        </div>

        <div class="station-body">

            <form action="{{ route('users.store') }}" method="post">
                @csrf
                <label for="old_password">Old Password</label>
                <input type="password" name="old_password" class="form-control form-control-custom">

                <label for="password">Password</label>
                <input type="password" name="password" class="form-control form-control-custom">

                <label for="password">Password</label>
                <input type="password" name="password_confirmation" class="form-control form-control-custom">

                <hr>

                <div class="d-flex justify-content-end gap-2 mt-4">
                   
                    <button type="submit" class="btn btn-save">
                        <i class="bi bi-check-circle me-1"></i> Save changes
                    </button>
                </div>
            </form>
      
        </div>
    </div>
       
 
@endsection

@section('styles')
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
@endsection




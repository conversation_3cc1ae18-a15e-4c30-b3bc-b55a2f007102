@extends('layouts.app')

@section('content')
<div class="container-fluid">

    <div class="d-flex flex-wrap gap-2 mb-3">
        <!-- Individual Action Buttons -->
        <button type="button" class="btn btn-sm btn-warning text-white" data-bs-toggle="modal" data-bs-target="#record_votes">
            <i class="bi bi-pencil-square"></i> <span class="button-text">Record Votes</span>
        </button>

        <button type="button" class="btn btn-sm btn-warning text-white" data-bs-toggle="modal" data-bs-target="#upload_evedence">
            <i class="bi bi-upload"></i> <span class="button-text">Upload Evidence</span>
        </button>

        <button type="button" class="btn btn-sm btn-danger text-white" data-bs-toggle="modal" data-bs-target="#record_spoiled_votes">
            <i class="bi bi-x-circle"></i> <span class="button-text">Record Spoiled Votes</span>
        </button>
    </div>
    <hr>   
 
    <div class="station-card">
        <div class="station-header flex-wrap">
            <h5 class="station-name mb-2 mb-sm-0">
                <i class="bi bi-building"></i>
                {{ $agent->polling_station->name }} Polling station
            </h5>
            <span class="badge bg-light text-dark">Station #{{ $agent->polling_station_id }}</span>
        </div>
        <div class="station-body">
            @if($agent)
            <div class="agent-info flex-wrap">
                <div class="agent-icon">
                    <i class="bi bi-person-vcard"></i>
                </div>
                <div class="agent-details">
                    <div class="agent-name">{{ $agent->user->name }}</div>
                    <div class="agent-phone">
                        <i class="bi bi-telephone me-1 small"></i>
                        {{ $agent->user->phone_number }}
                    </div>
                </div>
            </div>           
           @endif
            
            @foreach ($positions as $position)
            @php
                $totalVotes = $position->totalStationVotes($agent->polling_station->id);
            @endphp
            <div class="position-section position-results mt-4">
                <h6 class="mb-3"><i class="bi bi-award me-2 text-warning"></i>{{ $position->name }}</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm position-votes-table" data-position-id="{{ $position->id }}">
                        <thead class="table-light">
                            <tr>
                                <th>Candidate</th>
                                <th class="text-center">Votes</th>
                                <th class="text-end">Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($position->candidates as $candidate)
                            @php
                                $candidateVotes = $candidate->totalStationVotes($agent->polling_station->id);
                                $percentage = ($totalVotes > 0) ? round(($candidateVotes/$totalVotes * 100), 1) : 0;
                            @endphp
                            <tr>
                                <td>{{ $candidate->name }}</td>
                                <td class="text-center">{{ $candidateVotes }}</td>
                                <td class="text-end">
                                    <span class="badge" style="background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%)">
                                        {{ $percentage }}%
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center">{{ $totalVotes }}</th>
                                <th class="text-end">100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            @endforeach
            
            <!-- Spoiled Votes Section -->
            <div class="position-section position-results mt-4">
                <h6 class="mb-3"><i class="bi bi-x-circle me-2 text-danger"></i>Spoiled Votes</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm spoiled-votes-table">
                        <thead class="table-light">
                            <tr>
                                <th>Position</th>
                                <th class="text-center">Spoiled Votes</th>
                                <th class="text-end">Recorded On</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $spoiledVotes = \App\Models\SpoiledVote::where('polling_station_id', $agent->polling_station_id)
                                    ->with('position')
                                    ->get();
                                $totalSpoiled = $spoiledVotes->sum('number_of_votes');
                            @endphp
                            
                            @forelse ($spoiledVotes as $spoiledVote)
                            <tr>
                                <td>{{ $spoiledVote->position->name }}</td>
                                <td class="text-center">
                                    <span class="badge bg-danger">{{ $spoiledVote->number_of_votes }}</span>
                                </td>
                                <td class="text-end">
                                    <small class="text-muted">{{ $spoiledVote->created_at->format('M d, H:i') }}</small>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="text-center py-3">
                                    <span class="text-muted">No spoiled votes recorded yet</span>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                        @if($totalSpoiled > 0)
                        <tfoot class="table-light">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center">{{ $totalSpoiled }}</th>
                                <th class="text-end"></th>
                            </tr>
                        </tfoot>
                        @endif
                    </table>
                </div>
            </div>

             @if(count($agent->eveidences) > 0)
            <div class="evidence-list flex-wrap">
                @foreach ($agent->eveidences as $evidence)
                <a href="{{ asset('files/'.$evidence->file_url) }}" target="_blank" class="evidence-item mb-2">
                    <i class="bi bi-file-earmark-text"></i>
                    {{ $evidence->file_name }}
                </a>
                @endforeach
            </div>
            @endif
        </div>
    </div>
       


    <div class="modal fade" id="record_votes" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
        <div class="modal-header modal-header-custom">
            <h5 class="modal-title modal-title-custom" id="agentModalLabel">
                <i class="bi bi-person-plus me-2"></i> Record Votes
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body modal-body-custom">
            <form id="voteForm" action="{{ route('votes.store') }}" method="post">
                @csrf

                <div class="form-group mb-4">
                    <label for="position_select" class="form-label fw-bold">
                        <i class="bi bi-award text-warning me-2"></i>Select Position <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="bi bi-list-ul"></i>
                        </span>
                        <select name="position_id" id="position_select" class="form-control form-control-custom">
                            <option value="">-- Choose a position --</option>
                            @foreach ($positions as $position)
                                <option value="{{ $position->id }}">{{ $position->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Candidates Container -->
                <div id="candidates_container" style="display: none;">
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Enter vote counts for each candidate below:</strong>
                    </div>

                    <div id="candidates_list" class="candidates-grid">
                        <!-- Candidates will be loaded here dynamically -->
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-3 p-3 bg-light rounded">
                        <span class="fw-bold">Total Votes:</span>
                        <span id="total_votes" class="badge bg-primary fs-6">0</span>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </button>
                    <button type="submit" id="save_votes_btn" class="btn btn-save" disabled>
                        <i class="bi bi-check-circle me-1"></i> Save Votes
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>
</div>


<div class="modal fade" id="upload_evedence" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
        <div class="modal-header modal-header-custom">
            <h5 class="modal-title modal-title-custom" id="agentModalLabel">
                <i class="bi bi-person-plus me-2"></i> Upload Evedence
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body modal-body-custom">
            <form action="{{ route('evedence.store') }}" method="post" enctype="multipart/form-data">
                @csrf           
                
                <div class="form-group">
                    <label for="name" class="form-label">Select File <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>
                        <input type="file" name="picture">                        
                    </div>
                </div>                

                <div class="form-group">
                    <label for="phone_number" class="form-label">File Name <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-phone"></i>
                        </span>
                        <input type="text" name="file_name" class="form-control form-control-custom">
                    </div>                
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-save">
                        <i class="bi bi-check-circle me-1"></i> Save File
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>
    </div>

<!-- Modal for Recording Spoiled Votes -->
<div class="modal fade" id="record_spoiled_votes" tabindex="-1" aria-labelledby="spoiledVotesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
            <div class="modal-header modal-header-custom bg-danger text-white">
                <h5 class="modal-title modal-title-custom" id="spoiledVotesModalLabel">
                    <i class="bi bi-x-circle me-2"></i> Record Spoiled Votes
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body modal-body-custom">
                <form action="{{ route('spoiled_votes.store') }}" method="post">
                    @csrf
                    
                    <!-- Hidden field for polling station ID -->
                    <input type="hidden" name="polling_station_id" value="{{ $agent->polling_station_id }}">
                    
                    <div class="form-group mb-3">
                        <label for="position_id" class="form-label">Select Position <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-award"></i>
                            </span>
                            <select name="position_id" id="spoiled_position_id" class="form-control form-control-custom" required>
                                <option value="">Select a position</option>
                                @foreach ($positions as $position)
                                    <option value="{{ $position->id }}">{{ $position->name }}</option>                            
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="number_of_votes" class="form-label">Number of Spoiled Votes <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-hash"></i>
                            </span>
                            <input type="number" name="number_of_votes" class="form-control form-control-custom" min="0" required>
                        </div>                
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="remarks" class="form-label">Remarks (Optional)</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-chat-left-text"></i>
                            </span>
                            <textarea name="remarks" class="form-control form-control-custom" rows="3" placeholder="Describe the reason for spoiled votes (optional)"></textarea>
                        </div>                
                    </div>

                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-save me-1"></i> Record Spoiled Votes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/mobile.css') }}">
    <style>
        /* Enhanced button styling for submit all votes */
        .btn-primary {
            background: linear-gradient(135deg, #D4AF37 0%, #E8C547 100%);
            border: none;
            font-weight: 600;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #B8941F 0%, #D4AF37 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .btn-divider {
            width: 1px;
            background: #E5E7EB;
            margin: 0 0.5rem;
        }

        /* Candidate Cards Styling */
        .candidate-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef !important;
            transition: all 0.2s ease;
        }

        .candidate-card:hover {
            background: #fff;
            border-color: #D4AF37 !important;
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.1);
        }

        .candidate-info h6 {
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .vote-input-group .form-control {
            text-align: center;
            font-weight: 600;
        }

        .vote-input-group .form-control:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }

        .candidates-grid {
            max-height: 400px;
            overflow-y: auto;
        }

        .candidates-grid::-webkit-scrollbar {
            width: 6px;
        }

        .candidates-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .candidates-grid::-webkit-scrollbar-thumb {
            background: #D4AF37;
            border-radius: 3px;
        }

        .candidates-grid::-webkit-scrollbar-thumb:hover {
            background: #B8941F;
        }

        /* Responsive button text */
        @media (max-width: 768px) {
            .button-text {
                display: none;
            }

            .btn-lg {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .btn-divider {
                display: none;
            }

            .candidate-card {
                padding: 1rem !important;
            }

            .candidate-card .d-flex {
                flex-direction: column;
                gap: 0.75rem;
            }

            .vote-input-group {
                align-self: flex-end;
            }
        }
    </style>
@endsection

@push('scripts')
   <script type="text/javascript">
    let currentCandidates = [];

    document.getElementById('position_select').addEventListener('change', function(e) {
        var position_id = e.target.value;
        var candidatesContainer = document.getElementById('candidates_container');
        var candidatesList = document.getElementById('candidates_list');
        var saveBtn = document.getElementById('save_votes_btn');

        if (!position_id) {
            candidatesContainer.style.display = 'none';
            saveBtn.disabled = true;
            return;
        }

        // Show loading state
        candidatesList.innerHTML = '<div class="text-center p-3"><i class="bi bi-hourglass-split"></i> Loading candidates...</div>';
        candidatesContainer.style.display = 'block';

        fetch('/ajax_get_candidates/' + position_id)
            .then(response => response.json())
            .then(data => {
                currentCandidates = data;
                candidatesList.innerHTML = ''; // Clear loading message

                if (data.length === 0) {
                    candidatesList.innerHTML = '<div class="alert alert-warning">No candidates found for this position.</div>';
                    saveBtn.disabled = true;
                    return;
                }

                // Create candidate input fields
                data.forEach(function(candidate) {
                    var candidateCard = document.createElement('div');
                    candidateCard.className = 'candidate-card mb-3 p-3 border rounded';
                    candidateCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="candidate-info">
                                <h6 class="mb-1 fw-bold">${candidate.name}</h6>
                                <small class="text-muted">Candidate ID: ${candidate.id}</small>
                            </div>
                            <div class="vote-input-group">
                                <div class="input-group input-group-sm" style="width: 120px;">
                                    <input type="number"
                                           class="form-control vote-input"
                                           name="votes[${candidate.id}]"
                                           data-candidate-id="${candidate.id}"
                                           data-candidate-name="${candidate.name}"
                                           min="0"
                                           value="0"
                                           placeholder="0">
                                    <span class="input-group-text">votes</span>
                                </div>
                            </div>
                        </div>
                    `;
                    candidatesList.appendChild(candidateCard);
                });

                // Add event listeners to vote inputs
                addVoteInputListeners();
                saveBtn.disabled = false;
            })
            .catch(error => {
                console.error('Error fetching candidates:', error);
                candidatesList.innerHTML = '<div class="alert alert-danger">Error loading candidates. Please try again.</div>';
                saveBtn.disabled = true;
            });
    });

    function addVoteInputListeners() {
        const voteInputs = document.querySelectorAll('.vote-input');
        voteInputs.forEach(input => {
            input.addEventListener('input', updateTotalVotes);
        });
    }

    function updateTotalVotes() {
        const voteInputs = document.querySelectorAll('.vote-input');
        let total = 0;

        voteInputs.forEach(input => {
            const value = parseInt(input.value) || 0;
            total += value;
        });

        document.getElementById('total_votes').textContent = total;
    }

    // Handle form submission for multiple votes
    document.getElementById('voteForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const position_id = document.getElementById('position_select').value;

        if (!position_id) {
            alert('Please select a position first.');
            return;
        }

        // Collect all vote data
        const voteInputs = document.querySelectorAll('.vote-input');
        let hasVotes = false;

        voteInputs.forEach(input => {
            const votes = parseInt(input.value) || 0;
            if (votes > 0) {
                hasVotes = true;
            }
        });

        if (!hasVotes) {
            alert('Please enter at least one vote count.');
            return;
        }

        // Submit each candidate's votes individually
        const saveBtn = document.getElementById('save_votes_btn');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Saving...';

        let promises = [];

        voteInputs.forEach(input => {
            const candidateId = input.dataset.candidateId;
            const votes = parseInt(input.value) || 0;

            if (votes > 0) {
                const voteData = {
                    candidate_id: candidateId,
                    number_of_votes: votes,
                    _token: formData.get('_token')
                };

                promises.push(
                    fetch('{{ route("votes.store") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': voteData._token
                        },
                        body: JSON.stringify(voteData)
                    })
                );
            }
        });

        Promise.all(promises)
            .then(responses => {
                // Check if all requests were successful
                const allSuccessful = responses.every(response => response.ok);

                if (allSuccessful) {
                    alert('All votes saved successfully!');
                    // Reset form
                    document.getElementById('voteForm').reset();
                    document.getElementById('candidates_container').style.display = 'none';
                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('record_votes'));
                    modal.hide();
                    // Optionally reload page to show updated data
                    window.location.reload();
                } else {
                    alert('Some votes failed to save. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error saving votes:', error);
                alert('Error saving votes. Please try again.');
            })
            .finally(() => {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i> Save Votes';
            });
    });

    </script>
@endpush


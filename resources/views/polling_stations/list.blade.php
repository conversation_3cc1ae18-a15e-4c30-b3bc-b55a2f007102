@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: none;
        color: #333;
        font-weight: 600;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 215, 0, 0.3);
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 215, 0, 0.4);
        color: #333;
        background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .station-name {
        font-weight: 500;
        color: #333;
    }
    
    .constituency-badge {
        background-color: rgba(255, 215, 0, 0.15);
        color: #FF8C00;
        font-size: 0.8rem;
        padding: 0.35rem 0.75rem;
        border-radius: 30px;
        font-weight: 500;
        display: inline-block;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-agents {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .btn-agents:hover {
        background-color: #198754;
        color: white;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state-text {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 1.5rem;
    }
    


    .location-info {
        line-height: 1.3;
    }

    .location-info small {
        font-size: 0.8rem;
    }

    /* Stats Cards */
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .stats-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stats-icon.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .stats-icon.bg-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .stats-icon.bg-warning {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%) !important;
    }

    .stats-icon.bg-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    }

    /* Filter section styling */
    .form-select-sm:focus {
        border-color: #FFD700;
        box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    }

    /* Card View Styling */
    .station-card-item {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .station-card-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .station-card-item .card-title {
        color: #333;
        font-size: 1rem;
    }

    .station-card-item .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 6px;
    }

    /* View toggle buttons */
    .btn-group .btn.active {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-color: #FFD700;
        color: #333;
    }
</style>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">Polling Stations</h4>
            <p class="text-muted">Manage all polling stations in the election</p>
        </div>
        <div class="d-flex gap-2">
            
            <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_pollingstation">
                <i class="bi bi-plus-circle"></i> Add Polling Station
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-building text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->count() }}</h5>
                            <small class="text-muted">Total Stations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-geo-alt text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold" id="stationsWithCoordsCard">{{ $polling_stations->whereNotNull('latitude')->whereNotNull('longitude')->count() }}</h5>
                            <small class="text-muted">With Coordinates</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-people text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->sum(function($station) { return $station->agents->count(); }) }}</h5>
                            <small class="text-muted">Total Agents</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-geo text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->whereNotNull('district')->groupBy('district')->count() }}</h5>
                            <small class="text-muted">Districts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters Section -->
    <div class="card card-custom mb-4">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="bi bi-funnel me-2 text-warning"></i>
                    <span class="fw-medium">Advanced Filters</span>
                    <span class="badge bg-info ms-2">{{ $polling_stations->total() }} stations found</span>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('polling_stations.index') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <form method="GET" action="{{ route('polling_stations.index') }}" id="filtersForm">
                <div class="row g-3">
                    <!-- Search -->
                    <div class="col-md-3">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-search me-1"></i>Search
                        </label>
                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search stations, locations..."
                               value="{{ $currentFilters['search'] ?? '' }}">
                    </div>

                    <!-- District Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo-alt me-1"></i>District
                        </label>
                        <select name="district" class="form-select form-select-sm" id="districtFilter">
                            <option value="">All Districts</option>
                            @foreach($districts as $district)
                                <option value="{{ $district }}" {{ ($currentFilters['district'] ?? '') == $district ? 'selected' : '' }}>
                                    {{ $district }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- County Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-map me-1"></i>County
                        </label>
                        <select name="county" class="form-select form-select-sm">
                            <option value="">All Counties</option>
                            @foreach($counties as $county)
                                <option value="{{ $county }}" {{ ($currentFilters['county'] ?? '') == $county ? 'selected' : '' }}>
                                    {{ $county }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Subcounty Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-building me-1"></i>Subcounty
                        </label>
                        <select name="subcounty" class="form-select form-select-sm">
                            <option value="">All Subcounties</option>
                            @foreach($subcounties as $subcounty)
                                <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                    {{ $subcounty }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Coordinates Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo me-1"></i>Coordinates
                        </label>
                        <select name="coordinates" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['coordinates'] ?? '') == 'with' ? 'selected' : '' }}>With Coordinates</option>
                            <option value="without" {{ ($currentFilters['coordinates'] ?? '') == 'without' ? 'selected' : '' }}>Without Coordinates</option>
                        </select>
                    </div>

                    <!-- Agents Filter -->
                    <div class="col-md-1">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-people me-1"></i>Agents
                        </label>
                        <select name="agents" class="form-select form-select-sm">
                            <option value="">All</option>
                            <option value="with" {{ ($currentFilters['agents'] ?? '') == 'with' ? 'selected' : '' }}>With</option>
                            <option value="without" {{ ($currentFilters['agents'] ?? '') == 'without' ? 'selected' : '' }}>Without</option>
                        </select>
                    </div>
                </div>

                <div class="row g-3 mt-2">
                    <!-- Results Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-check-circle me-1"></i>Results
                        </label>
                        <select name="results" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="submitted" {{ ($currentFilters['results'] ?? '') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                            <option value="pending" {{ ($currentFilters['results'] ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>

                    <!-- Evidence Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-file-earmark me-1"></i>Evidence
                        </label>
                        <select name="evidence" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['evidence'] ?? '') == 'with' ? 'selected' : '' }}>With Evidence</option>
                            <option value="without" {{ ($currentFilters['evidence'] ?? '') == 'without' ? 'selected' : '' }}>Without Evidence</option>
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-sort-down me-1"></i>Sort By
                        </label>
                        <select name="sort_by" class="form-select form-select-sm">
                            <option value="name" {{ ($currentFilters['sort_by'] ?? 'name') == 'name' ? 'selected' : '' }}>Name</option>
                            <option value="district" {{ ($currentFilters['sort_by'] ?? '') == 'district' ? 'selected' : '' }}>District</option>
                            <option value="county" {{ ($currentFilters['sort_by'] ?? '') == 'county' ? 'selected' : '' }}>County</option>
                            <option value="created_at" {{ ($currentFilters['sort_by'] ?? '') == 'created_at' ? 'selected' : '' }}>Date Created</option>
                        </select>
                    </div>

                    <!-- Sort Order -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-arrow-up-down me-1"></i>Order
                        </label>
                        <select name="sort_order" class="form-select form-select-sm">
                            <option value="asc" {{ ($currentFilters['sort_order'] ?? 'asc') == 'asc' ? 'selected' : '' }}>Ascending</option>
                            <option value="desc" {{ ($currentFilters['sort_order'] ?? '') == 'desc' ? 'selected' : '' }}>Descending</option>
                        </select>
                    </div>

                    <!-- Per Page -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-list me-1"></i>Per Page
                        </label>
                        <select name="per_page" class="form-select form-select-sm">
                            <option value="10" {{ ($currentFilters['per_page'] ?? 20) == 10 ? 'selected' : '' }}>10</option>
                            <option value="20" {{ ($currentFilters['per_page'] ?? 20) == 20 ? 'selected' : '' }}>20</option>
                            <option value="50" {{ ($currentFilters['per_page'] ?? 20) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($currentFilters['per_page'] ?? 20) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="bi bi-funnel me-1"></i>Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    
    <!-- Main Content Card -->
    <div class="card card-custom" id="listViewContainer">
        
        <div class="card-body-custom p-4">
            @if(count($polling_stations) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Coordinates</th>
                            <th>Agents</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($polling_stations as $polling_station)
                            <tr data-station-id="{{ $polling_station->id }}">
                                <td>
                                    <span class="station-name">{{ $polling_station->name }}</span>
                                </td>
                                <td>
                                    @if($polling_station->district || $polling_station->county || $polling_station->subcounty)
                                        <div class="location-info">
                                            @if($polling_station->district)
                                                <div class="text-primary fw-bold">{{ $polling_station->district }} District</div>
                                            @endif
                                            @if($polling_station->county)
                                                <small class="text-muted">{{ $polling_station->county }}</small>
                                            @endif
                                            @if($polling_station->subcounty)
                                                <small class="text-muted"> • {{ $polling_station->subcounty }}</small>
                                            @endif
                                            @if($polling_station->parish)
                                                <small class="text-muted"> • {{ $polling_station->parish }}</small>
                                            @endif
                                            @if($polling_station->village)
                                                <div><small class="badge bg-light text-dark">{{ $polling_station->village }}</small></div>
                                            @endif
                                        </div>
                                    @elseif($polling_station->constituency)
                                        <span class="constituency-badge">{{ $polling_station->constituency }}</span>
                                    @else
                                        <span class="text-muted">Location not specified</span>
                                    @endif
                                </td>
                                <td>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <span class="badge bg-success coordinates-badge" 
                                              data-lat="{{ $polling_station->latitude }}" 
                                              data-lng="{{ $polling_station->longitude }}" 
                                              data-name="{{ $polling_station->name }}">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            {{ number_format($polling_station->latitude, 4) }}, {{ number_format($polling_station->longitude, 4) }}
                                        </span>
                                    @else
                                        <span class="badge bg-light text-muted">
                                            <i class="bi bi-geo-alt-fill me-1"></i>
                                            Not set
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        {{ $polling_station->agents->count() }} agents
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-action btn-agents">
                                            <i class="bi bi-people"></i> Agents
                                        </a>
                                        <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        @if($polling_station->agents->count() == 0)
                                            <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this polling station?')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>                            
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Card View (Hidden by default) -->
            <div id="cardViewContainer" style="display: none;">
                @if(count($polling_stations) > 0)
                <div class="row g-3">
                    @foreach ($polling_stations as $polling_station)
                    <div class="col-md-6 col-lg-4 station-card"
                         data-name="{{ strtolower($polling_station->name) }}"
                         data-district="{{ $polling_station->district ?? '' }}"
                         data-county="{{ $polling_station->county ?? '' }}"
                         data-has-coordinates="{{ ($polling_station->latitude && $polling_station->longitude) ? 'true' : 'false' }}"
                         data-agents-count="{{ $polling_station->agents->count() }}">
                        <div class="card station-card-item border-0 shadow-sm h-100">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0 fw-bold">{{ $polling_station->name }}</h6>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <span class="badge bg-success">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @else
                                        <span class="badge bg-light text-muted">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @endif
                                </div>

                                <!-- Location Info -->
                                @if($polling_station->district || $polling_station->county || $polling_station->subcounty)
                                <div class="location-info mb-2">
                                    @if($polling_station->district)
                                        <div class="text-primary fw-bold small">{{ $polling_station->district }} District</div>
                                    @endif
                                    @if($polling_station->county)
                                        <small class="text-muted">{{ $polling_station->county }}</small>
                                    @endif
                                    @if($polling_station->subcounty)
                                        <small class="text-muted"> • {{ $polling_station->subcounty }}</small>
                                    @endif
                                    @if($polling_station->village)
                                        <div><small class="badge bg-light text-dark mt-1">{{ $polling_station->village }}</small></div>
                                    @endif
                                </div>
                                @elseif($polling_station->constituency)
                                <div class="mb-2">
                                    <span class="constituency-badge">{{ $polling_station->constituency }}</span>
                                </div>
                                @endif

                                <!-- Stats -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-people me-1"></i>{{ $polling_station->agents->count() }} agents
                                    </small>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <small class="text-success">
                                            <i class="bi bi-geo-alt me-1"></i>Mapped
                                        </small>
                                    @else
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt me-1"></i>No coordinates
                                        </small>
                                    @endif
                                </div>

                                <!-- Actions -->
                                <div class="d-flex gap-1">
                                    <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-sm btn-outline-success flex-fill">
                                        <i class="bi bi-people"></i> Agents
                                    </a>
                                    <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    @if($polling_station->agents->count() == 0)
                                        <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <h5 class="empty-state-text">No polling stations found</h5>
                    <a href="{{ route('polling_stations.create') }}" class="btn btn-add">
                        <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                    </a>
                </div>
                @endif
            </div>

            @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-building"></i>
                </div>
                <h5 class="empty-state-text">No polling stations found</h5>
                <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_pollingstation">
                    <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                </button>
            </div>
            @endif

            <!-- Pagination Controls -->
            @if($polling_stations->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="showing-entries">
                    <small class="text-muted">
                        Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                        of {{ $polling_stations->total() }} stations
                    </small>
                </div>
                <div class="pagination-container">
                    {{ $polling_stations->links('pagination::bootstrap-4') }}
                </div>
            </div>
            @endif
        </div>
    </div>
</div>





<style>
    .coordinates-badge {
        cursor: pointer;
        transition: all 0.2s;
    }
    .coordinates-badge:hover {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: scale(1.05);
    }

    .loading-overlay.show {
        display: flex;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View switching functionality
        const tableViewBtn = document.getElementById('tableView');
        const cardViewBtn = document.getElementById('cardView');
        const tableContainer = document.querySelector('.table-responsive');
        const cardContainer = document.getElementById('cardViewContainer');

        // Set initial view (table)
        tableViewBtn.classList.add('active');

        tableViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'block';
            cardContainer.style.display = 'none';
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
        });

        cardViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'none';
            cardContainer.style.display = 'block';
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
        });

        // Enhanced server-side filtering functionality
        const filtersForm = document.getElementById('filtersForm');
        const searchInput = document.querySelector('input[name="search"]');
        const districtFilter = document.getElementById('districtFilter');

        // Auto-submit form when filters change (with debouncing)
        let filterTimeout;

        function debounceFilter(callback, delay = 500) {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(callback, delay);
        }

        // Auto-submit on filter change
        if (filtersForm) {
            const filterInputs = filtersForm.querySelectorAll('select, input[name="search"]');

            filterInputs.forEach(input => {
                if (input.name === 'search') {
                    // Debounce search input
                    input.addEventListener('input', function() {
                        debounceFilter(() => {
                            showLoadingOverlay();
                            filtersForm.submit();
                        });
                    });
                } else {
                    // Immediate submit for select filters
                    input.addEventListener('change', function() {
                        showLoadingOverlay();
                        filtersForm.submit();
                    });
                }
            });
        }

        // Loading overlay functions
        function showLoadingOverlay() {
            let overlay = document.querySelector('.loading-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Applying filters...</div>
                    </div>
                `;
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    z-index: 9999;
                    align-items: center;
                    justify-content: center;
                `;
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        }

        function hideLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // Hide loading overlay when page loads
        hideLoadingOverlay();

        // Performance optimization: Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));



                if (selectedAgents === 'with' && station.agentsCount === 0) {
                    visible = false;
                } else if (selectedAgents === 'without' && station.agentsCount > 0) {
                    visible = false;
                }

                // Apply visibility to both table row and card
                if (station.tableRow) {
                    station.tableRow.style.display = visible ? '' : 'none';
                }
                if (station.cardElement) {
                    station.cardElement.style.display = visible ? '' : 'none';
                }

                if (visible) visibleCount++;
            });

            // Update visible count
            updateVisibleCount(visibleCount);
        }

        // Update county filter based on district selection
        function updateCountyFilter(selectedDistrict) {
            countyFilter.innerHTML = '<option value="">All Counties</option>';

            if (selectedDistrict) {
                const counties = new Set();
                stationsData.forEach(station => {
                    if (station.district === selectedDistrict && station.county) {
                        counties.add(station.county);
                    }
                });

                counties.forEach(county => {
                    const option = document.createElement('option');
                    option.value = county;
                    option.textContent = county;
                    countyFilter.appendChild(option);
                });

                countyFilter.disabled = counties.size === 0;
            } else {
                countyFilter.disabled = true;
            }
        }

        // Update visible count display
        function updateVisibleCount(count) {
            const totalCount = stationsData.length;
            const countDisplay = document.querySelector('.card-header-custom .fw-medium');
            if (countDisplay) {
                countDisplay.innerHTML = `All Polling Stations <span class="badge bg-info ms-2">${count} of ${totalCount} shown</span>`;
            }
        }

        // Event listeners with error handling
        if (searchInput) {
            searchInput.addEventListener('keyup', applyFilters);
        } else {
            console.error('Search input not found');
        }

        if (districtFilter) {
            districtFilter.addEventListener('change', function() {
                updateCountyFilter(this.value);
                if (countyFilter) countyFilter.value = ''; // Reset county selection
                applyFilters();
            });
        }

        if (countyFilter) {
            countyFilter.addEventListener('change', applyFilters);
        }

        if (coordinatesFilter) {
            coordinatesFilter.addEventListener('change', applyFilters);
        }

        if (agentsFilter) {
            agentsFilter.addEventListener('change', applyFilters);
        }

        // Clear filters
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function() {
                if (searchInput) searchInput.value = '';
                if (districtFilter) districtFilter.value = '';
                if (countyFilter) countyFilter.value = '';
                if (coordinatesFilter) coordinatesFilter.value = '';
                if (agentsFilter) agentsFilter.value = '';
                updateCountyFilter('');
                applyFilters();
            });
        }

        // Initialize
        console.log('Stations data loaded:', stationsData.length);
        updateVisibleCount(stationsData.length);

        // Debug: Log first few stations data
        if (stationsData.length > 0) {
            console.log('Sample station data:', stationsData.slice(0, 3));
        }
        


        // Function to check if coordinates are within Uganda
        function isWithinUganda(lat, lng) {
            // Uganda's approximate bounding box
            const ugandaBounds = {
                minLat: -1.5, // Southern border
                maxLat: 4.3,  // Northern border
                minLng: 29.5, // Western border
                maxLng: 35.0  // Eastern border
            };

            return lat >= ugandaBounds.minLat &&
                   lat <= ugandaBounds.maxLat &&
                   lng >= ugandaBounds.minLng &&
                   lng <= ugandaBounds.maxLng;
        }








    });
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.form-control:focus, .form-select:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    font-weight: 600;
    color: #333;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    color: #333;
}

.btn-warning {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    color: #333;
    font-weight: 600;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    color: #333;
}

.text-primary {
    color: #FF8C00 !important;
}

.text-warning {
    color: #FF8C00 !important;
}

.alert-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.bg-light {
    background: linear-gradient(135deg, #fffef7 0%, #f8f9fa 100%) !important;
    border-radius: 8px;
}
</style>

@endsection

<!-- Add Polling Station Modal -->
@include('polling_stations.partials.add_modal')

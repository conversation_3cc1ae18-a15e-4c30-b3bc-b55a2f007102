@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-plus-circle me-2"></i>{{ $title }}
                        </h5>
                        <a href="{{ route('polling_stations.index') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>Back
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('polling_stations.store') }}" method="post" id="pollingStationForm">
                        @csrf

                        <!-- Station Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-bold text-primary">
                                <i class="bi bi-building me-1"></i>Station Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" name="name" id="name"
                                   class="form-control @error('name') is-invalid @enderror"
                                   placeholder="Enter polling station name"
                                   value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Administrative Location - Compact Design -->
                        <div class="card mb-3 border-0 bg-light">
                            <div class="card-body p-3">
                                <h6 class="text-primary fw-bold mb-2">
                                    <i class="bi bi-geo-alt me-2"></i>Location Details
                                </h6>

                                <!-- Row 1: District & County -->
                                <div class="row g-2 mb-2">
                                    <div class="col-6">
                                        <label for="district" class="form-label small fw-bold">District <span class="text-danger">*</span></label>
                                        <select name="district" id="district" class="form-select form-select-sm @error('district') is-invalid @enderror" required>
                                            <option value="">Select District</option>
                                        </select>
                                        @error('district')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                    <div class="col-6">
                                        <label for="county" class="form-label small fw-bold">County <span class="text-danger">*</span></label>
                                        <select name="county" id="county" class="form-select form-select-sm @error('county') is-invalid @enderror" required disabled>
                                            <option value="">Select County</option>
                                        </select>
                                        @error('county')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                </div>

                                <!-- Row 2: Subcounty & Parish -->
                                <div class="row g-2 mb-2">
                                    <div class="col-6">
                                        <label for="subcounty" class="form-label small fw-bold">Subcounty <span class="text-danger">*</span></label>
                                        <select name="subcounty" id="subcounty" class="form-select form-select-sm @error('subcounty') is-invalid @enderror" required disabled>
                                            <option value="">Select Subcounty</option>
                                        </select>
                                        @error('subcounty')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                    <div class="col-6">
                                        <label for="parish" class="form-label small fw-bold">Parish <span class="text-danger">*</span></label>
                                        <select name="parish" id="parish" class="form-select form-select-sm @error('parish') is-invalid @enderror" required disabled>
                                            <option value="">Select Parish</option>
                                        </select>
                                        @error('parish')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                </div>

                                <!-- Row 3: Village & Constituency -->
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="village" class="form-label small fw-bold">Village <span class="text-danger">*</span></label>
                                        <input type="text" name="village" id="village" class="form-control form-control-sm @error('village') is-invalid @enderror"
                                               placeholder="Village name" value="{{ old('village') }}" required>
                                        @error('village')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                    <div class="col-6">
                                        <label for="constituency" class="form-label small fw-bold">Constituency</label>
                                        <input type="text" name="constituency" id="constituency" class="form-control form-control-sm @error('constituency') is-invalid @enderror"
                                               placeholder="Optional" value="{{ old('constituency') }}">
                                        @error('constituency')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GPS Coordinates with Address Lookup - Compact -->
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body p-3">
                                <h6 class="text-warning fw-bold mb-2">
                                    <i class="bi bi-geo me-1"></i>GPS Coordinates (Optional)
                                </h6>

                                <!-- Address Lookup -->
                                <div class="mb-2">
                                    <label class="form-label small fw-bold">Find by Address</label>
                                    <div class="input-group input-group-sm">
                                        <input type="text" id="address-lookup" class="form-control" placeholder="Enter location in Uganda">
                                        <button class="btn btn-warning" type="button" id="geocode-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                    <div id="geocode-results" class="d-none alert alert-success alert-sm mt-2 py-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small><i class="bi bi-check-circle me-1"></i><span id="geocode-address"></span></small>
                                            <button type="button" id="apply-coordinates" class="btn btn-success btn-sm">Use</button>
                                        </div>
                                    </div>
                                    <div id="geocode-error" class="d-none alert alert-danger alert-sm mt-2 py-2">
                                        <small><i class="bi bi-exclamation-triangle me-1"></i><span id="error-message"></span></small>
                                    </div>
                                </div>

                                <!-- Manual Input -->
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="latitude" class="form-label small">Latitude</label>
                                        <input type="number" step="any" id="latitude" name="latitude"
                                               class="form-control form-control-sm @error('latitude') is-invalid @enderror"
                                               placeholder="0.3476" value="{{ old('latitude') }}">
                                        @error('latitude')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                    <div class="col-6">
                                        <label for="longitude" class="form-label small">Longitude</label>
                                        <input type="number" step="any" id="longitude" name="longitude"
                                               class="form-control form-control-sm @error('longitude') is-invalid @enderror"
                                               placeholder="32.5825" value="{{ old('longitude') }}">
                                        @error('longitude')<div class="invalid-feedback">{{ $message }}</div>@enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        @error('coordinates')
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>{{ $message }}
                        </div>
                        @enderror

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between pt-3 border-top">
                            <a href="{{ route('polling_stations.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="bi bi-check-lg me-1"></i>Create Station
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Uganda administrative data
    const ugandaData = {
        'Oyam': {
            counties: {
                'Oyam County North': {
                    subcounties: ['Acaba', 'Aleka', 'Anyeke', 'Iceme', 'Kamdini', 'Loro', 'Myene', 'Ngai', 'Otwal'],
                    parishes: {
                        'Acaba': ['Acaba Central', 'Acaba East', 'Acaba West'],
                        'Aleka': ['Aleka Central', 'Aleka North', 'Aleka South'],
                        'Anyeke': ['Anyeke Central', 'Anyeke East', 'Anyeke West'],
                        'Iceme': ['Iceme Central', 'Iceme North', 'Iceme South'],
                        'Kamdini': ['Kamdini Central', 'Kamdini East', 'Kamdini West'],
                        'Loro': ['Loro Central', 'Loro North', 'Loro South'],
                        'Myene': ['Myene Central', 'Myene East', 'Myene West'],
                        'Ngai': ['Ngai Central', 'Ngai North', 'Ngai South'],
                        'Otwal': ['Otwal Central', 'Otwal East', 'Otwal West']
                    }
                },
                'Oyam County South': {
                    subcounties: ['Abok', 'Minakulu', 'Ngai', 'Okwang', 'Otuke'],
                    parishes: {
                        'Abok': ['Abok Central', 'Abok East', 'Abok West'],
                        'Minakulu': ['Minakulu Central', 'Minakulu North', 'Minakulu South'],
                        'Ngai': ['Ngai Central', 'Ngai East', 'Ngai West'],
                        'Okwang': ['Okwang Central', 'Okwang North', 'Okwang South'],
                        'Otuke': ['Otuke Central', 'Otuke East', 'Otuke West']
                    }
                }
            }
        },
        'Kampala': {
            counties: {
                'Kampala Central': {
                    subcounties: ['Central Division', 'Kawempe Division', 'Makindye Division', 'Nakawa Division', 'Rubaga Division'],
                    parishes: {
                        'Central Division': ['Kisenyi I', 'Kisenyi II', 'Mengo', 'Nsambya'],
                        'Kawempe Division': ['Bwaise I', 'Bwaise II', 'Kawempe I', 'Kawempe II'],
                        'Makindye Division': ['Katwe I', 'Katwe II', 'Kibuye I', 'Kibuye II'],
                        'Nakawa Division': ['Bukoto I', 'Bukoto II', 'Naguru', 'Nakawa'],
                        'Rubaga Division': ['Lubaga', 'Nakulabye', 'Rubaga', 'Lungujja']
                    }
                }
            }
        },
        'Gulu': {
            counties: {
                'Gulu Municipality': {
                    subcounties: ['Bardege Division', 'Layibi Division', 'Laroo Division', 'Pece Division'],
                    parishes: {
                        'Bardege Division': ['Bardege', 'Kaunda', 'Orapwoyo'],
                        'Layibi Division': ['Layibi', 'Cwero', 'Tegot'],
                        'Laroo Division': ['Laroo', 'Akokoro', 'Pabbo'],
                        'Pece Division': ['Pece', 'Unyama', 'Awach']
                    }
                },
                'Aswa County': {
                    subcounties: ['Awach', 'Bungatira', 'Koro', 'Palaro', 'Patiko'],
                    parishes: {
                        'Awach': ['Awach Central', 'Awach East', 'Awach West'],
                        'Bungatira': ['Bungatira Central', 'Bungatira North', 'Bungatira South'],
                        'Koro': ['Koro Central', 'Koro East', 'Koro West'],
                        'Palaro': ['Palaro Central', 'Palaro North', 'Palaro South'],
                        'Patiko': ['Patiko Central', 'Patiko East', 'Patiko West']
                    }
                }
            }
        },
        'Lira': {
            counties: {
                'Lira Municipality': {
                    subcounties: ['Adekokwok', 'Lira Central', 'Ojwina', 'Railways'],
                    parishes: {
                        'Adekokwok': ['Adekokwok Central', 'Adekokwok East', 'Adekokwok West'],
                        'Lira Central': ['Central Ward', 'Market Ward', 'Hospital Ward'],
                        'Ojwina': ['Ojwina Central', 'Ojwina North', 'Ojwina South'],
                        'Railways': ['Railways Central', 'Railways East', 'Railways West']
                    }
                },
                'Erute County': {
                    subcounties: ['Agweng', 'Aloi', 'Aromo', 'Barr', 'Dokolo'],
                    parishes: {
                        'Agweng': ['Agweng Central', 'Agweng East', 'Agweng West'],
                        'Aloi': ['Aloi Central', 'Aloi North', 'Aloi South'],
                        'Aromo': ['Aromo Central', 'Aromo East', 'Aromo West'],
                        'Barr': ['Barr Central', 'Barr North', 'Barr South'],
                        'Dokolo': ['Dokolo Central', 'Dokolo East', 'Dokolo West']
                    }
                }
            }
        },
        'Arua': {
            counties: {
                'Arua Municipality': {
                    subcounties: ['Arua Hill Division', 'River Oli Division', 'Tanganyika Division'],
                    parishes: {
                        'Arua Hill Division': ['Arua Hill', 'Mvara', 'Oli River'],
                        'River Oli Division': ['River Oli', 'Ediofe', 'Pajulu'],
                        'Tanganyika Division': ['Tanganyika', 'Manibe', 'Dadamu']
                    }
                },
                'Terego County': {
                    subcounties: ['Ayivu', 'Oluko', 'Rigbo', 'Terego'],
                    parishes: {
                        'Ayivu': ['Ayivu Central', 'Ayivu East', 'Ayivu West'],
                        'Oluko': ['Oluko Central', 'Oluko North', 'Oluko South'],
                        'Rigbo': ['Rigbo Central', 'Rigbo East', 'Rigbo West'],
                        'Terego': ['Terego Central', 'Terego North', 'Terego South']
                    }
                }
            }
        }
    };

    // Get form elements
    const districtSelect = document.getElementById('district');
    const countySelect = document.getElementById('county');
    const subcountySelect = document.getElementById('subcounty');
    const parishSelect = document.getElementById('parish');

    // Populate districts
    function populateDistricts() {
        Object.keys(ugandaData).forEach(district => {
            const option = document.createElement('option');
            option.value = district;
            option.textContent = district;
            if (district === 'Oyam') {
                option.selected = true; // Set Oyam as default
            }
            districtSelect.appendChild(option);
        });
        
        // Trigger change event to populate counties for default district
        if (districtSelect.value === 'Oyam') {
            populateCounties('Oyam');
        }
    }

    // Populate counties based on selected district
    function populateCounties(district) {
        countySelect.innerHTML = '<option value="">Select County</option>';
        subcountySelect.innerHTML = '<option value="">Select Subcounty</option>';
        parishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (district && ugandaData[district]) {
            Object.keys(ugandaData[district].counties).forEach(county => {
                const option = document.createElement('option');
                option.value = county;
                option.textContent = county;
                countySelect.appendChild(option);
            });
            countySelect.disabled = false;
        } else {
            countySelect.disabled = true;
            subcountySelect.disabled = true;
            parishSelect.disabled = true;
        }
    }

    // Populate subcounties based on selected county
    function populateSubcounties(district, county) {
        subcountySelect.innerHTML = '<option value="">Select Subcounty</option>';
        parishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (district && county && ugandaData[district] && ugandaData[district].counties[county]) {
            ugandaData[district].counties[county].subcounties.forEach(subcounty => {
                const option = document.createElement('option');
                option.value = subcounty;
                option.textContent = subcounty;
                subcountySelect.appendChild(option);
            });
            subcountySelect.disabled = false;
        } else {
            subcountySelect.disabled = true;
            parishSelect.disabled = true;
        }
    }

    // Populate parishes based on selected subcounty
    function populateParishes(district, county, subcounty) {
        parishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (district && county && subcounty && 
            ugandaData[district] && 
            ugandaData[district].counties[county] && 
            ugandaData[district].counties[county].parishes[subcounty]) {
            
            ugandaData[district].counties[county].parishes[subcounty].forEach(parish => {
                const option = document.createElement('option');
                option.value = parish;
                option.textContent = parish;
                parishSelect.appendChild(option);
            });
            parishSelect.disabled = false;
        } else {
            parishSelect.disabled = true;
        }
    }

    // Event listeners
    districtSelect.addEventListener('change', function() {
        populateCounties(this.value);
    });

    countySelect.addEventListener('change', function() {
        populateSubcounties(districtSelect.value, this.value);
    });

    subcountySelect.addEventListener('change', function() {
        populateParishes(districtSelect.value, countySelect.value, this.value);
    });

    // Initialize
    populateDistricts();

    // Address lookup functionality
    const addressInput = document.getElementById('address-lookup');
    const geocodeBtn = document.getElementById('geocode-btn');
    const geocodeResults = document.getElementById('geocode-results');
    const geocodeError = document.getElementById('geocode-error');
    const latitudeInput = document.getElementById('latitude');
    const longitudeInput = document.getElementById('longitude');
    const applyBtn = document.getElementById('apply-coordinates');

    let foundLat = null;
    let foundLng = null;

    // Function to check if coordinates are within Uganda
    function isWithinUganda(lat, lng) {
        const ugandaBounds = {
            minLat: -1.5, maxLat: 4.3, minLng: 29.5, maxLng: 35.0
        };
        return lat >= ugandaBounds.minLat && lat <= ugandaBounds.maxLat &&
               lng >= ugandaBounds.minLng && lng <= ugandaBounds.maxLng;
    }

    // Geocode address function
    async function geocodeAddress(address) {
        geocodeResults.classList.add('d-none');
        geocodeError.classList.add('d-none');
        geocodeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';

        try {
            const ugandaViewbox = '29.5,4.3,35.0,-1.5';
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&viewbox=${ugandaViewbox}&bounded=1&countrycodes=ug`);
            const data = await response.json();

            if (data && data.length > 0) {
                const result = data[0];
                foundLat = parseFloat(result.lat);
                foundLng = parseFloat(result.lon);

                if (isWithinUganda(foundLat, foundLng)) {
                    document.getElementById('geocode-address').textContent = result.display_name.substring(0, 50) + '...';
                    geocodeResults.classList.remove('d-none');
                } else {
                    document.getElementById('error-message').textContent = 'Location not in Uganda.';
                    geocodeError.classList.remove('d-none');
                }
            } else {
                document.getElementById('error-message').textContent = 'No location found.';
                geocodeError.classList.remove('d-none');
            }
        } catch (error) {
            document.getElementById('error-message').textContent = 'Search failed. Try again.';
            geocodeError.classList.remove('d-none');
        }

        geocodeBtn.innerHTML = '<i class="bi bi-search"></i>';
    }

    // Event listeners for address lookup
    geocodeBtn.addEventListener('click', function() {
        const address = addressInput.value.trim();
        if (address) {
            geocodeAddress(address);
        }
    });

    addressInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            geocodeBtn.click();
        }
    });

    applyBtn.addEventListener('click', function() {
        if (foundLat !== null && foundLng !== null) {
            latitudeInput.value = foundLat;
            longitudeInput.value = foundLng;
            geocodeResults.classList.add('d-none');
        }
    });
});
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.form-control:focus, .form-select:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-body {
    background: linear-gradient(135deg, #fffef7 0%, #ffffff 100%);
}

.form-label.small {
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    color: #333;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    color: #333;
}

.btn-warning {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    color: #333;
    font-weight: 600;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    color: #333;
}

.btn-outline-secondary {
    border-radius: 8px;
    font-weight: 600;
}

.form-control-sm, .form-select-sm {
    border-radius: 6px;
}

.bg-light {
    background: linear-gradient(135deg, #fffef7 0%, #f8f9fa 100%) !important;
    border-radius: 10px;
}

.text-primary {
    color: #FF8C00 !important;
}

.text-warning {
    color: #FF8C00 !important;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
</style>
@endsection

@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Compact Modern Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="header-card bg-gradient-primary text-white rounded-4 p-3 shadow-sm">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="header-icon-wrapper me-3">
                            <i class="bi bi-graph-up-arrow fs-3"></i>
                        </div>
                        <div>
                            <h1 class="h5 mb-0 fw-bold">Candidate Monitoring</h1>
                            <small class="text-white-50">Track preferred candidates vs leaders</small>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('home') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-house me-1"></i>Home
                        </a>
                        <button class="btn btn-outline-light btn-sm" data-bs-toggle="collapse" data-bs-target="#helpCollapse">
                            <i class="bi bi-question-circle"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Collapsible Help -->
                <div class="collapse mt-3" id="helpCollapse">
                    <div class="bg-white bg-opacity-10 rounded-3 p-3">
                        <small class="text-white-75">
                            <strong>How it works:</strong> Select candidates below to monitor them. 
                            Leading candidates are compared with 2nd place, trailing candidates with the leader.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Statistics -->
    @if(count($preferredCandidates) > 0)
    @php
        $leadingCount = 0;
        $trailingCount = 0;
        $alertsCount = 0;
        
        foreach($preferredCandidates as $candidate) {
            $gapInfo = $monitoringData[$candidate->id] ?? null;
            if (!$gapInfo) continue;
            
            if ($gapInfo['is_leading']) {
                $leadingCount++;
            } else {
                $trailingCount++;
            }
            
            $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
            if ($alertThreshold && $gapInfo['gap'] < $alertThreshold) {
                $alertsCount++;
            }
        }
    @endphp
    
    <div class="row mb-3">
        <div class="col-12">
            <div class="stats-bar bg-white rounded-3 shadow-sm p-3">
                <div class="row g-3">
                    <div class="col-6 col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-value text-primary fw-bold fs-4">{{ count($preferredCandidates) }}</div>
                            <div class="stat-label text-muted small">Monitored</div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-value text-success fw-bold fs-4">{{ $leadingCount }}</div>
                            <div class="stat-label text-muted small">Leading</div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-value text-danger fw-bold fs-4">{{ $trailingCount }}</div>
                            <div class="stat-label text-muted small">Trailing</div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="stat-item text-center">
                            <div class="stat-value text-warning fw-bold fs-4">{{ $alertsCount }}</div>
                            <div class="stat-label text-muted small">Alerts</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Compact Monitoring Panel -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="monitoring-panel bg-white rounded-3 shadow-sm">
                <!-- Panel Header with Filters -->
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center gap-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-star-fill text-warning me-2"></i>
                            <h6 class="mb-0 fw-bold">Preferred Candidates</h6>
                            @if(count($preferredCandidates) > 0)
                                <span class="badge bg-primary ms-2">{{ count($preferredCandidates) }}</span>
                            @endif
                        </div>
                        
                        @if(count($preferredCandidates) > 0)
                        <div class="filter-controls d-flex gap-2">
                            <input type="text" id="candidateSearchInput" class="form-control form-control-sm" placeholder="Search..." style="width: 150px;">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                                <option value="">All Status</option>
                                <option value="leading">Leading</option>
                                <option value="trailing">Trailing</option>
                            </select>
                            <select class="form-select form-select-sm" id="alertFilter" style="width: auto;">
                                <option value="">All Alerts</option>
                                <option value="triggered">Triggered</option>
                                <option value="safe">Safe</option>
                            </select>
                            <button class="btn btn-outline-secondary btn-sm" id="clearFilters">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Candidates List -->
                <div class="panel-body p-3">
                    @if(count($preferredCandidates) > 0)
                        <div class="row g-3" id="candidatesContainer">
                            @foreach($preferredCandidates as $candidate)
                                @php
                                    $gapInfo = $monitoringData[$candidate->id] ?? null;
                                    if (!$gapInfo) continue;
                                    
                                    $isLeading = $gapInfo['is_leading'];
                                    $gap = $gapInfo['gap'];
                                    $competitor = $gapInfo['competitor'];
                                    $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
                                    $isAlertTriggered = $alertThreshold && $gap < $alertThreshold;
                                    
                                    $totalVotes = $gapInfo['preferred_votes'] + $gapInfo['competitor_votes'];
                                    $preferredPercentage = $totalVotes > 0 ? ($gapInfo['preferred_votes'] / $totalVotes) * 100 : 50;
                                    
                                    $statusColor = $isLeading ? 'success' : 'danger';
                                @endphp
                                
                                <div class="col-md-6 candidate-card" 
                                     data-name="{{ strtolower($candidate->name) }}"
                                     data-status="{{ $isLeading ? 'leading' : 'trailing' }}"
                                     data-position="{{ $candidate->position_id }}"
                                     data-alert="{{ $isAlertTriggered ? 'triggered' : 'safe' }}">
                                    
                                    <!-- Compact Candidate Card -->
                                    <div class="candidate-compact-card border rounded-3 p-3 h-100 {{ $isAlertTriggered ? 'border-warning' : 'border-light' }}">
                                        <!-- Header Row -->
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div class="d-flex align-items-center">
                                                <!-- Avatar -->
                                                <div class="candidate-avatar me-2">
                                                    @if(isset($candidate->picture))
                                                        <img src="{{ asset('files/'.$candidate->picture) }}" alt="{{ $candidate->name }}" class="rounded-circle" width="40" height="40">
                                                    @else
                                                        @php
                                                            $nameParts = explode(' ', $candidate->name);
                                                            $initials = count($nameParts) >= 2 ? 
                                                                strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1)) : 
                                                                strtoupper(substr($candidate->name, 0, 2));
                                                            $bgColor = $candidate->party_color ?? ($statusColor == 'success' ? '#28a745' : '#dc3545');
                                                        @endphp
                                                        <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                             style="background: {{ $bgColor }}; color: white; font-weight: bold; width: 40px; height: 40px; font-size: 0.9rem;">
                                                            {{ $initials }}
                                                        </div>
                                                    @endif
                                                </div>
                                                
                                                <!-- Name & Position -->
                                                <div>
                                                    <h6 class="mb-0 fw-bold">{{ $candidate->name }}</h6>
                                                    <small class="text-muted">{{ $candidate->position->name }}</small>
                                                </div>
                                            </div>
                                            
                                            <!-- Status Badge -->
                                            <div class="d-flex flex-column align-items-end">
                                                <span class="badge bg-{{ $statusColor }} mb-1">
                                                    @if($isLeading)
                                                        1st Place
                                                    @else
                                                        {{ $gapInfo['preferred_position'] ?? 'N/A' }}{{ $gapInfo['preferred_position'] == 2 ? 'nd' : ($gapInfo['preferred_position'] == 3 ? 'rd' : 'th') }} Place
                                                    @endif
                                                </span>
                                                @if($isAlertTriggered)
                                                    <span class="badge bg-warning text-dark small">
                                                        <i class="bi bi-exclamation-triangle-fill"></i> Alert
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        <!-- Vote Stats Row -->
                                        <div class="row g-2 mb-2">
                                            <div class="col-6">
                                                <div class="vote-stat text-center p-2 bg-{{ $statusColor }} bg-opacity-10 rounded-2">
                                                    <div class="fw-bold text-{{ $statusColor }}">{{ number_format($gapInfo['preferred_votes']) }}</div>
                                                    <small class="text-muted">Your Votes</small>
                                                </div>
                                            </div>
                                            @if($competitor)
                                            <div class="col-6">
                                                <div class="vote-stat text-center p-2 bg-light rounded-2">
                                                    <div class="fw-bold text-secondary">{{ number_format($gapInfo['competitor_votes']) }}</div>
                                                    <small class="text-muted">{{ $isLeading ? '2nd Place' : 'Leader' }}</small>
                                                </div>
                                            </div>
                                            @endif
                                        </div>
                                        
                                        <!-- Gap & Progress -->
                                        @if($competitor)
                                        <div class="gap-info mb-2">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">
                                                    @if($isLeading)
                                                        Leading by
                                                    @else
                                                        Behind by
                                                    @endif
                                                </small>
                                                <small class="fw-bold text-{{ $statusColor }}">{{ number_format($gap) }} votes</small>
                                            </div>
                                            <div class="progress" style="height: 4px;">
                                                <div class="progress-bar bg-{{ $statusColor }}" style="width: {{ $preferredPercentage }}%"></div>
                                            </div>
                                        </div>
                                        @endif
                                        
                                        <!-- Action Button -->
                                        <div class="text-center">
                                            <a href="{{ route('monitoring.compare', $candidate) }}" class="btn btn-{{ $statusColor }} btn-sm">
                                                <i class="bi bi-bar-chart-line me-1"></i>Analyze
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-star text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                            <h6 class="text-muted mt-3">No Preferred Candidates</h6>
                            <p class="text-muted small">Select candidates below to start monitoring</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Candidate Selection -->
    <div class="row">
        <div class="col-12">
            <div class="selection-panel bg-white rounded-3 shadow-sm">
                <!-- Panel Header -->
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-people-fill text-primary me-2"></i>
                            <h6 class="mb-0 fw-bold">All Candidates</h6>
                        </div>
                        <select class="form-select form-select-sm" id="positionSelector" style="width: auto;">
                            <option value="">All Positions</option>
                            @foreach($positions as $position)
                                <option value="{{ $position->id }}">{{ $position->name }} ({{ count($position->candidates) }})</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Candidates Grid -->
                <div class="panel-body p-3">
                    @foreach($positions as $position)
                        <div class="position-section mb-4" data-position="{{ $position->id }}">
                            <h6 class="fw-bold text-primary mb-3 d-flex align-items-center">
                                <i class="bi bi-award me-2"></i>{{ $position->name }}
                                <span class="badge bg-light text-dark ms-2">{{ count($position->candidates) }} candidates</span>
                            </h6>

                            <div class="row g-2">
                                @foreach($position->candidates as $candidate)
                                    @php
                                        $isPreferred = in_array($candidate->id, $preferredCandidates->pluck('id')->toArray());
                                        $totalVotes = $candidate->totalVotes();
                                    @endphp

                                    <div class="col-md-4 col-lg-3">
                                        <div class="candidate-selection-card border rounded-2 p-2 {{ $isPreferred ? 'border-warning bg-warning bg-opacity-10' : 'border-light' }}">
                                            <div class="d-flex align-items-center">
                                                <!-- Avatar -->
                                                <div class="candidate-avatar me-2">
                                                    @if(isset($candidate->picture))
                                                        <img src="{{ asset('files/'.$candidate->picture) }}" alt="{{ $candidate->name }}" class="rounded-circle" width="32" height="32">
                                                    @else
                                                        @php
                                                            $nameParts = explode(' ', $candidate->name);
                                                            $initials = count($nameParts) >= 2 ?
                                                                strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1)) :
                                                                strtoupper(substr($candidate->name, 0, 2));
                                                            $bgColor = $candidate->party_color ?? '#6c757d';
                                                        @endphp
                                                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                             style="background: {{ $bgColor }}; color: white; font-weight: bold; width: 32px; height: 32px; font-size: 0.8rem;">
                                                            {{ $initials }}
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- Info -->
                                                <div class="flex-grow-1">
                                                    <div class="fw-bold small">{{ $candidate->name }}</div>
                                                    <div class="text-muted small">{{ number_format($totalVotes) }} votes</div>
                                                </div>

                                                <!-- Toggle Button -->
                                                <div>
                                                    @if($isPreferred)
                                                        <form action="{{ route('monitoring.remove', $candidate) }}" method="post" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-warning btn-sm">
                                                                <i class="bi bi-star-fill"></i>
                                                            </button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('monitoring.add', $candidate) }}" method="post" class="d-inline">
                                                            @csrf
                                                            <button type="submit" class="btn btn-outline-secondary btn-sm">
                                                                <i class="bi bi-star"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    /* Modern Compact Design */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .header-card {
        border: none;
        backdrop-filter: blur(10px);
    }

    .stats-bar {
        border: none;
    }

    .stat-item {
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-2px);
    }

    .monitoring-panel, .selection-panel {
        border: none;
        transition: all 0.3s ease;
    }

    .monitoring-panel:hover, .selection-panel:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .candidate-compact-card {
        transition: all 0.3s ease;
        background: #fff;
    }

    .candidate-compact-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .candidate-selection-card {
        transition: all 0.3s ease;
        background: #fff;
    }

    .candidate-selection-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .panel-header {
        border-bottom: 1px solid #e9ecef;
    }

    .vote-stat {
        transition: all 0.3s ease;
    }

    .vote-stat:hover {
        transform: scale(1.02);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
    }

    .badge {
        font-weight: 600;
    }

    .progress {
        border-radius: 2px;
    }

    .rounded-2 {
        border-radius: 8px !important;
    }

    .rounded-3 {
        border-radius: 12px !important;
    }

    .rounded-4 {
        border-radius: 16px !important;
    }

    .text-white-50 {
        color: rgba(255,255,255,0.7) !important;
    }

    .text-white-75 {
        color: rgba(255,255,255,0.9) !important;
    }

    .position-section {
        transition: all 0.3s ease;
    }

    .position-section.d-none {
        opacity: 0;
        transform: translateY(-10px);
    }

    /* Full width utilization */
    .container-fluid {
        max-width: none !important;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Optimize for wider screens */
    @media (min-width: 1400px) {
        .container-fluid {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .col-lg-4 {
            flex: 0 0 auto;
            width: 20%; /* 5 cards per row instead of 3 */
        }

        .col-md-6 {
            flex: 0 0 auto;
            width: 33.333333%; /* 3 cards per row instead of 2 */
        }
    }

    @media (min-width: 1920px) {
        .container-fluid {
            padding-left: 3rem;
            padding-right: 3rem;
        }

        .col-lg-4 {
            width: 16.666667%; /* 6 cards per row */
        }

        .col-md-6 {
            width: 25%; /* 4 cards per row */
        }
    }

    .filter-controls {
        flex-wrap: wrap;
    }

    @media (max-width: 768px) {
        .filter-controls {
            width: 100%;
            justify-content: stretch;
        }

        .filter-controls > * {
            flex: 1;
            min-width: 0;
        }
    }
</style>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Position filter functionality
        const positionSelector = document.getElementById('positionSelector');
        const positionSections = document.querySelectorAll('.position-section');

        if (positionSelector) {
            positionSelector.addEventListener('change', function() {
                const selectedPositionId = this.value;

                positionSections.forEach(section => {
                    if (selectedPositionId === '' || section.dataset.position === selectedPositionId) {
                        section.classList.remove('d-none');
                    } else {
                        section.classList.add('d-none');
                    }
                });
            });
        }

        // Candidate filtering functionality
        const candidateSearchInput = document.getElementById('candidateSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const alertFilter = document.getElementById('alertFilter');
        const clearFiltersBtn = document.getElementById('clearFilters');
        const candidateCards = document.querySelectorAll('.candidate-card');

        function applyFilters() {
            const searchTerm = candidateSearchInput ? candidateSearchInput.value.toLowerCase() : '';
            const selectedStatus = statusFilter ? statusFilter.value : '';
            const selectedAlert = alertFilter ? alertFilter.value : '';

            candidateCards.forEach(card => {
                let visible = true;

                // Search filter
                if (searchTerm && !card.dataset.name.includes(searchTerm)) {
                    visible = false;
                }

                // Status filter
                if (selectedStatus && card.dataset.status !== selectedStatus) {
                    visible = false;
                }

                // Alert filter
                if (selectedAlert && card.dataset.alert !== selectedAlert) {
                    visible = false;
                }

                card.style.display = visible ? '' : 'none';
            });
        }

        // Event listeners
        if (candidateSearchInput) {
            candidateSearchInput.addEventListener('keyup', applyFilters);
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', applyFilters);
        }

        if (alertFilter) {
            alertFilter.addEventListener('change', applyFilters);
        }

        // Clear filters
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function() {
                if (candidateSearchInput) candidateSearchInput.value = '';
                if (statusFilter) statusFilter.value = '';
                if (alertFilter) alertFilter.value = '';
                applyFilters();
            });
        }
    });
</script>
@endpush

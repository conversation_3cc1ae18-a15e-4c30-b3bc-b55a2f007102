@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <!-- Dashboard Header -->
    <div class="dashboard-header rounded-3 bg-white shadow-sm p-4 mb-4">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center">
            <div class="mb-3 mb-md-0">
                <div class="d-flex align-items-center mb-2">
                    <div class="rounded-circle bg-info bg-opacity-10 p-2 me-3">
                        <i class="bi bi-book-fill text-info fs-4"></i>
                    </div>
                    <h1 class="dashboard-title fs-2 fw-bold text-primary mb-0">Monitoring Guide</h1>
                </div>
                <p class="dashboard-subtitle text-muted mb-0 fs-5 ps-5">How to use the candidate monitoring features</p>
            </div>
            
            <div>
                <a href="{{ route('monitoring.index') }}" class="btn btn-outline-primary px-4 py-2 d-flex align-items-center">
                    <i class="bi bi-arrow-left me-2"></i> Back to Monitoring
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm rounded-3 border-0 mb-5">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                            <i class="bi bi-lightbulb-fill text-primary fs-4"></i>
                        </div>
                        <h3 class="fw-bold mb-0">Getting Started with Candidate Monitoring</h3>
                    </div>
                    <p class="fs-5 mb-4 ps-5">The Preferred Candidate Monitoring feature allows you to closely track specific candidates of interest, receive alerts when vote gaps fall below thresholds, and analyze detailed comparisons against competitors.</p>
                    
                    <div class="alert alert-info rounded-3 border-start border-info border-4 p-4 ms-5">
                        <div class="d-flex">
                            <i class="bi bi-info-circle-fill text-info fs-4 me-3"></i>
                            <p class="mb-0">This guide will help you understand how to use all the monitoring features effectively. Follow the steps below to get the most out of the candidate monitoring system.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <h3 class="mb-4 fw-bold d-flex align-items-center">
                <div class="rounded-circle bg-success bg-opacity-10 p-2 me-3">
                    <i class="bi bi-list-ol text-success fs-4"></i>
                </div>
                Step-by-Step Guide
            </h3>
            
            <!-- Step 1 -->
            <div class="card shadow-sm rounded-3 border-0 mb-4 position-relative overflow-hidden">
                <!-- Step indicator -->
                <div class="position-absolute top-0 start-0 bg-primary text-white px-3 py-2 rounded-bottom-end shadow-sm">
                    <span class="fw-bold">STEP 1</span>
                </div>
                
                <div class="card-body p-4 pt-5 mt-3">
                    <h4 class="card-title fw-bold mb-3 d-flex align-items-center">
                        <i class="bi bi-star-fill text-warning me-3 fs-4"></i>
                        Adding Preferred Candidates
                    </h4>
                    
                    <p class="fs-5 mb-4">Start by selecting candidates you want to monitor:</p>
                    
                    <div class="bg-light p-4 rounded-3 mb-4">
                        <ol class="ps-3">
                            <li class="mb-3"><span class="fw-bold">Navigate to the</span> <span class="badge bg-primary px-3 py-2"><i class="bi bi-speedometer2 me-1"></i> Candidate Monitoring</span> page</li>
                            <li class="mb-3"><span class="fw-bold">Scroll down</span> to the <span class="fw-bold text-primary">All Candidates</span> section</li>
                            <li class="mb-3"><span class="fw-bold">Click the</span> <span class="badge bg-success px-3 py-2"><i class="bi bi-eye me-1"></i> Monitor</span> button next to any candidate</li>
                            <li class="mb-3"><span class="fw-bold">The candidate will appear</span> in the <span class="fw-bold text-primary">Preferred Candidates</span> section</li>
                            <li><span class="fw-bold">View preferred candidates</span> on the main dashboard</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-warning rounded-3 border-start border-warning border-4 p-4">
                        <div class="d-flex">
                            <i class="bi bi-lightbulb-fill text-warning fs-4 me-3"></i>
                            <p class="mb-0">Tip: Select candidates from different positions to get a comprehensive view of your campaign's performance across various electoral races.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 2 -->
            <div class="card shadow-sm rounded-3 border-0 mb-4 position-relative overflow-hidden">
                <!-- Step indicator -->
                <div class="position-absolute top-0 start-0 bg-primary text-white px-3 py-2 rounded-bottom-end shadow-sm">
                    <span class="fw-bold">STEP 2</span>
                </div>
                
                <div class="card-body p-4 pt-5 mt-3">
                    <h4 class="card-title fw-bold mb-3 d-flex align-items-center">
                        <i class="bi bi-gear-fill text-primary me-3 fs-4"></i>
                        Configuring Monitoring Settings
                    </h4>
                    
                    <p class="fs-5 mb-4">Customize how you track each preferred candidate:</p>
                    
                    <div class="bg-light p-4 rounded-3 mb-4">
                        <ol class="ps-3">
                            <li class="mb-3"><span class="fw-bold">Click the</span> <span class="badge bg-primary px-3 py-2"><i class="bi bi-gear me-1"></i> Settings</span> button for any preferred candidate</li>
                            <li class="mb-3"><span class="fw-bold">Add</span> optional <span class="fw-bold text-primary">Monitoring Notes</span> about this candidate</li>
                            <li class="mb-3"><span class="fw-bold">Set a</span> <span class="fw-bold text-success">Target Votes</span> number for your goal</li>
                            <li class="mb-3"><span class="fw-bold">Configure a</span> <span class="fw-bold text-danger">Vote Gap Alert Threshold</span> for notifications</li>
                            <li><span class="fw-bold">Click</span> <span class="badge bg-success px-3 py-2"><i class="bi bi-check-circle me-1"></i> Save Settings</span> to apply changes</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-primary rounded-3 border-start border-primary border-4 p-4">
                        <div class="d-flex">
                            <i class="bi bi-info-circle-fill text-primary fs-4 me-3"></i>
                            <p class="mb-0">Setting a target vote count helps you track progress toward your goals, while the alert threshold ensures you're notified when the race gets too close for comfort.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 3 -->
            <div class="card shadow-sm rounded-3 border-0 mb-4 position-relative overflow-hidden">
                <!-- Step indicator -->
                <div class="position-absolute top-0 start-0 bg-primary text-white px-3 py-2 rounded-bottom-end shadow-sm">
                    <span class="fw-bold">STEP 3</span>
                </div>
                
                <div class="card-body p-4 pt-5 mt-3">
                    <h4 class="card-title fw-bold mb-3 d-flex align-items-center">
                        <i class="bi bi-bar-chart-line-fill text-success me-3 fs-4"></i>
                        Analyze Candidate Performance
                    </h4>
                    
                    <p class="fs-5 mb-4">Analyze the performance of your preferred candidates:</p>
                    
                    <div class="bg-light p-4 rounded-3 mb-4">
                        <ol class="ps-3">
                            <li class="mb-3"><span class="fw-bold">Go to the</span> <a href="{{ route('candidates.index') }}" class="btn btn-sm btn-outline-primary"><i class="bi bi-people me-1"></i> Candidates List</a></li>
                            <li class="mb-3"><span class="fw-bold">Find the candidate</span> you want to analyze</li>
                            <li class="mb-3"><span class="fw-bold">Click the</span> <span class="badge bg-success px-3 py-2"><i class="bi bi-bar-chart me-1"></i> Analyze</span> button</li>
                            <li class="mb-3"><span class="fw-bold">View the candidate's performance metrics</span></li>
                            <li><span class="fw-bold">Use the insights to adjust your campaign strategy</span></li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-success rounded-3 border-start border-success border-4 p-4">
                        <div class="d-flex">
                            <i class="bi bi-check-circle-fill text-success fs-4 me-3"></i>
                            <p class="mb-0">By analyzing candidate performance, you can make data-driven decisions to improve your campaign's chances of success.</p>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-bar-chart-line-fill text-success" style="font-size: 3rem;"></i>
                                <p class="mt-2 text-muted">Analyze detailed performance metrics</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 4 -->
            <div class="card shadow-sm rounded-3 border-0 mb-4 position-relative overflow-hidden">
                <!-- Step indicator -->
                <div class="position-absolute top-0 start-0 bg-primary text-white px-3 py-2 rounded-bottom-end shadow-sm">
                    <span class="fw-bold">STEP 4</span>
                </div>
                
                <div class="card-body p-4 pt-5 mt-3">
                    <h4 class="card-title fw-bold mb-3 d-flex align-items-center">
                        <i class="bi bi-bell-fill text-danger me-3 fs-4"></i>
                        Receiving and Managing Alerts
                    </h4>
                    
                    <p class="fs-5 mb-4">Stay informed about critical changes in vote gaps:</p>
                    
                    <div class="bg-light p-4 rounded-3 mb-4">
                        <ol class="ps-3">
                            <li class="mb-3"><span class="fw-bold">Receive notifications</span> when vote gaps fall below your threshold</li>
                            <li class="mb-3"><span class="fw-bold">Check the</span> <span class="badge bg-danger px-3 py-2"><i class="bi bi-bell me-1"></i> notification bell</span> in the navigation bar</li>
                            <li class="mb-3"><span class="fw-bold">Click on any alert</span> to see details</li>
                            <li class="mb-3"><span class="fw-bold">Select</span> <span class="badge bg-primary px-3 py-2"><i class="bi bi-eye me-1"></i> View</span> to go to the comparison page</li>
                            <li class="mb-3"><span class="fw-bold">Access all notifications</span> via <span class="fw-bold text-primary">View all notifications</span></li>
                            <li><span class="fw-bold">Check your email</span> for critical alert notifications</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-danger rounded-3 border-start border-danger border-4 p-4">
                        <div class="d-flex">
                            <i class="bi bi-exclamation-triangle-fill text-danger fs-4 me-3"></i>
                            <p class="mb-0">Alerts help you respond quickly when races get close. Timely intervention based on these alerts can make the difference between winning and losing a close race.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Testing the System -->
            <div class="card shadow-sm rounded-3 border-0 mb-4">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-secondary bg-opacity-10 p-3 me-3">
                            <i class="bi bi-terminal-fill text-secondary fs-4"></i>
                        </div>
                        <h3 class="fw-bold mb-0">Testing the Alert System</h3>
                    </div>
                    
                    <p class="fs-5 mb-4 ps-5">Administrators can test the alert system using the following command in the terminal:</p>
                    
                    <div class="bg-dark text-light p-4 rounded-3 ms-5 mb-4 border-start border-info border-4">
                        <code class="fs-5">php artisan app:trigger-test-alert</code>
                    </div>
                    
                    <div class="ms-5">
                        <p class="fw-bold mb-3">This command allows you to:</p>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item bg-transparent d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-3"></i>
                                Select any candidate to generate a test alert
                            </li>
                            <li class="list-group-item bg-transparent d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-3"></i>
                                Simulate leading or trailing scenarios
                            </li>
                            <li class="list-group-item bg-transparent d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-3"></i>
                                Specify a custom vote gap
                            </li>
                            <li class="list-group-item bg-transparent d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-3"></i>
                                Send test notifications to all admin users
                            </li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info rounded-3 border-start border-info border-4 p-4 mt-4 ms-5">
                        <div class="d-flex">
                            <i class="bi bi-info-circle-fill text-info fs-4 me-3"></i>
                            <p class="mb-0">The system automatically checks for vote gap alerts every 5 minutes and sends notifications when thresholds are crossed.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Conclusion -->
            <div class="card shadow-sm rounded-3 border-0 mb-4 bg-primary bg-opacity-10">
                <div class="card-body p-4 text-center">
                    <h3 class="fw-bold mb-3">Ready to Start Monitoring</h3>
                    <p class="fs-5 mb-4">You're now equipped with all the knowledge needed to effectively monitor your preferred candidates.</p>
                    <a href="{{ route('monitoring.index') }}" class="btn btn-primary px-4 py-2">
                        <i class="bi bi-speedometer2 me-2"></i> Go to Monitoring Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

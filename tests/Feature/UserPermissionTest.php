<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserPermissionTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that new users get default user_type of 'agent'
     */
    public function test_new_user_gets_default_agent_type()
    {
        $user = User::create([
            'name' => 'Test User',
            'phone_number' => '+256700000001',
            'password' => bcrypt('password'),
        ]);

        $this->assertEquals('agent', $user->user_type);
        $this->assertTrue($user->is_active);
    }

    /**
     * Test that hasPermission method works correctly for agent users
     */
    public function test_agent_user_has_correct_permissions()
    {
        $user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '+256700000002',
            'password' => bcrypt('password'),
            'user_type' => 'agent',
        ]);

        // Agent should have these permissions
        $this->assertTrue($user->hasPermission('submit_votes'));
        $this->assertTrue($user->hasPermission('upload_evidence'));
        $this->assertTrue($user->hasPermission('view_assigned_station'));
        $this->assertTrue($user->hasPermission('view_dashboard'));

        // Agent should NOT have these permissions
        $this->assertFalse($user->hasPermission('view_users'));
        $this->assertFalse($user->hasPermission('manage_system'));
        $this->assertFalse($user->hasPermission('create_polling_stations'));
    }

    /**
     * Test that admin users have all permissions
     */
    public function test_admin_user_has_all_permissions()
    {
        $user = User::create([
            'name' => 'Test Admin',
            'phone_number' => '+256700000003',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
        ]);

        // Admin should have all permissions
        $this->assertTrue($user->hasPermission('submit_votes'));
        $this->assertTrue($user->hasPermission('view_users'));
        $this->assertTrue($user->hasPermission('manage_system'));
        $this->assertTrue($user->hasPermission('create_polling_stations'));
        $this->assertTrue($user->hasPermission('any_permission_name'));
    }

    /**
     * Test that manager users have correct permissions
     */
    public function test_manager_user_has_correct_permissions()
    {
        $user = User::create([
            'name' => 'Test Manager',
            'phone_number' => '+256700000004',
            'password' => bcrypt('password'),
            'user_type' => 'manager',
        ]);

        // Manager should have these permissions
        $this->assertTrue($user->hasPermission('view_polling_stations'));
        $this->assertTrue($user->hasPermission('create_polling_stations'));
        $this->assertTrue($user->hasPermission('edit_polling_stations'));
        $this->assertTrue($user->hasPermission('manage_polling_stations'));
        $this->assertTrue($user->hasPermission('view_votes'));
        $this->assertTrue($user->hasPermission('view_dashboard'));
        $this->assertTrue($user->hasPermission('view_analytics'));
        $this->assertTrue($user->hasPermission('view_reports'));

        // Manager should NOT have these permissions
        $this->assertFalse($user->hasPermission('manage_system'));
        $this->assertFalse($user->hasPermission('view_users'));
    }

    /**
     * Test that viewer users have correct permissions
     */
    public function test_viewer_user_has_correct_permissions()
    {
        $user = User::create([
            'name' => 'Test Viewer',
            'phone_number' => '+256700000005',
            'password' => bcrypt('password'),
            'user_type' => 'viewer',
        ]);

        // Viewer should have these permissions
        $this->assertTrue($user->hasPermission('view_dashboard'));
        $this->assertTrue($user->hasPermission('view_analytics'));
        $this->assertTrue($user->hasPermission('view_reports'));
        $this->assertTrue($user->hasPermission('view_polling_stations'));
        $this->assertTrue($user->hasPermission('view_votes'));
        $this->assertTrue($user->hasPermission('view_candidates'));

        // Viewer should NOT have these permissions
        $this->assertFalse($user->hasPermission('submit_votes'));
        $this->assertFalse($user->hasPermission('create_polling_stations'));
        $this->assertFalse($user->hasPermission('manage_system'));
        $this->assertFalse($user->hasPermission('view_users'));
    }

    /**
     * Test that hasPermission method doesn't throw errors
     */
    public function test_has_permission_method_exists_and_works()
    {
        $user = User::create([
            'name' => 'Test User',
            'phone_number' => '+256700000006',
            'password' => bcrypt('password'),
        ]);

        // This should not throw an "undefined method" error
        $result = $user->hasPermission('any_permission');
        $this->assertIsBool($result);
    }
}

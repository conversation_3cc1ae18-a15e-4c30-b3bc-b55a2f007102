<?php

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\Candidate;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\User;
use App\Models\Vote;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PollingManagerTest extends TestCase
{
    use RefreshDatabase;

    protected $manager;
    protected $pollingStation;
    protected $agent;
    protected $position;
    protected $candidate;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a manager user
        $this->manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'manager',
            'is_active' => true,
        ]);

        // Create a polling station
        $this->pollingStation = PollingStation::create([
            'name' => 'Test Polling Station',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user
        $agentUser = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000001',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create agent record
        $this->agent = Agent::create([
            'user_id' => $agentUser->id,
            'polling_station_id' => $this->pollingStation->id,
        ]);

        // Create position and candidate
        $this->position = Position::create([
            'name' => 'President',
        ]);

        $this->candidate = Candidate::create([
            'name' => 'Test Candidate',
            'position_id' => $this->position->id,
        ]);
    }

    /**
     * Test that manager can access dashboard
     */
    public function test_manager_can_access_dashboard()
    {
        $this->actingAs($this->manager);

        $response = $this->get('/manager/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Polling Manager Dashboard');
        $response->assertSee($this->pollingStation->name);
    }

    /**
     * Test that manager is redirected to dashboard after login
     */
    public function test_manager_redirected_to_dashboard_after_login()
    {
        $response = $this->post('/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $response->assertRedirect('/manager/dashboard');
        $this->assertAuthenticatedAs($this->manager);
    }

    /**
     * Test that manager can view vote submission form
     */
    public function test_manager_can_view_vote_form()
    {
        $this->actingAs($this->manager);

        $response = $this->get("/manager/station/{$this->pollingStation->id}/vote-form");

        $response->assertStatus(200);
        $response->assertSee('Submit Votes');
        $response->assertSee($this->pollingStation->name);
        $response->assertSee($this->candidate->name);
    }

    /**
     * Test that manager can submit votes for a polling station
     */
    public function test_manager_can_submit_votes()
    {
        $this->actingAs($this->manager);

        $voteData = [
            'votes' => [
                $this->candidate->id => 150
            ]
        ];

        $response = $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", $voteData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that it redirects to the manager dashboard
        $this->assertTrue(str_contains($response->headers->get('Location'), 'manager/dashboard'));

        // Verify vote was recorded
        $this->assertDatabaseHas('votes', [
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 150,
        ]);
    }

    /**
     * Test that manager can view evidence upload form
     */
    public function test_manager_can_view_evidence_form()
    {
        $this->actingAs($this->manager);

        $response = $this->get("/manager/station/{$this->pollingStation->id}/evidence-form");

        $response->assertStatus(200);
        $response->assertSee('Upload Evidence');
        $response->assertSee($this->pollingStation->name);
    }

    /**
     * Test that manager can upload evidence
     */
    public function test_manager_can_upload_evidence()
    {
        Storage::fake('public');
        $this->actingAs($this->manager);

        $file = UploadedFile::fake()->image('evidence.jpg');

        $response = $this->post("/manager/station/{$this->pollingStation->id}/upload-evidence", [
            'evidence_files' => [$file],
            'file_names' => ['Test Evidence']
        ]);

        $response->assertRedirect('/manager/dashboard');
        $response->assertSessionHas('success');

        // Verify evidence was recorded
        $this->assertDatabaseHas('eveidences', [
            'agent_id' => $this->agent->id,
            'file_name' => 'Test Evidence',
        ]);
    }

    /**
     * Test dashboard filtering functionality
     */
    public function test_dashboard_filtering_works()
    {
        $this->actingAs($this->manager);

        // Test search filter
        $response = $this->get('/manager/dashboard?search=Test');
        $response->assertStatus(200);
        $response->assertSee($this->pollingStation->name);

        // Test district filter
        $response = $this->get('/manager/dashboard?district=Test District');
        $response->assertStatus(200);
        $response->assertSee($this->pollingStation->name);

        // Test status filter
        $response = $this->get('/manager/dashboard?status=pending');
        $response->assertStatus(200);
        $response->assertSee($this->pollingStation->name);
    }

    /**
     * Test that non-managers cannot access manager routes
     */
    public function test_non_managers_cannot_access_manager_routes()
    {
        $viewer = User::create([
            'name' => 'Test Viewer',
            'phone_number' => '0700000002',
            'password' => Hash::make('password'),
            'user_type' => 'viewer',
            'is_active' => true,
        ]);

        $this->actingAs($viewer);

        $response = $this->get('/manager/dashboard');
        $response->assertRedirect('/home');
        $response->assertSessionHas('error');
    }

    /**
     * Test station details API endpoint
     */
    public function test_station_details_api()
    {
        $this->actingAs($this->manager);

        // Add a vote to test the summary
        Vote::create([
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 100,
        ]);

        $response = $this->get("/manager/station/{$this->pollingStation->id}/details");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'station',
            'vote_summary',
            'evidence_count'
        ]);
    }

    /**
     * Test that manager cannot submit votes for station without agent
     */
    public function test_cannot_submit_votes_without_agent()
    {
        $this->actingAs($this->manager);

        // Create station without agent
        $stationWithoutAgent = PollingStation::create([
            'name' => 'Station Without Agent',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        $response = $this->post("/manager/station/{$stationWithoutAgent->id}/submit-votes", [
            'votes' => [$this->candidate->id => 50]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'No agent assigned to this polling station.');
    }
}

<?php

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\Candidate;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\User;
use App\Models\Vote;
use App\Models\VoteAuditLog;
use App\Services\VoteAuditService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VoteAuditTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $candidate;
    protected $position;
    protected $pollingStation;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '+256700000999',
            'password' => bcrypt('password'),
            'user_type' => 'agent',
            'is_active' => true
        ]);

        $this->pollingStation = PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District'
        ]);

        $this->agent = Agent::create([
            'user_id' => $this->user->id,
            'polling_station_id' => $this->pollingStation->id
        ]);

        $this->position = Position::create([
            'name' => 'President'
        ]);

        $this->candidate = Candidate::create([
            'name' => 'Test Candidate',
            'position_id' => $this->position->id
        ]);
    }

    /**
     * Test that vote submissions are logged in audit trail
     */
    public function test_vote_submission_creates_audit_log()
    {
        // Create a Sanctum token for the user
        $token = $this->user->createToken('test-token')->plainTextToken;

        // Submit a vote via API with Bearer token
        $response = $this->postJson('/api/record_votes', [
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 150,
            'latitude' => 2.3456,
            'longitude' => 32.1234
        ], [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(200);

        // Check that audit log was created
        $this->assertDatabaseHas('vote_audit_logs', [
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'new_votes' => 150,
            'action_type' => 'create',
            'submission_method' => 'api'
        ]);

        $auditLog = VoteAuditLog::first();
        $this->assertEquals(0, $auditLog->previous_votes);
        $this->assertEquals(150, $auditLog->vote_difference);
    }

    /**
     * Test that vote updates are logged with correct differences
     */
    public function test_vote_update_logs_difference()
    {
        // Create a Sanctum token for the user
        $token = $this->user->createToken('test-token')->plainTextToken;

        // Create initial vote
        Vote::create([
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 100
        ]);

        // Update the vote
        $response = $this->postJson('/api/record_votes', [
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 175,
            'latitude' => 2.3456,
            'longitude' => 32.1234
        ], [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(200);

        // Check audit log
        $auditLog = VoteAuditLog::first();
        $this->assertEquals(100, $auditLog->previous_votes);
        $this->assertEquals(175, $auditLog->new_votes);
        $this->assertEquals(75, $auditLog->vote_difference);
        $this->assertEquals('update', $auditLog->action_type);
    }

    /**
     * Test that large vote changes are automatically flagged
     */
    public function test_large_vote_changes_are_flagged()
    {
        $auditService = new VoteAuditService();

        // Log a submission with large vote change
        $auditLog = $auditService->logVoteSubmission(
            $this->agent->id,
            $this->candidate->id,
            1500, // Large number
            'create',
            'api'
        );

        // Should be flagged for large vote change
        $this->assertTrue($auditLog->is_flagged);
        $this->assertEquals('large_vote_change', $auditLog->flag_reason);
    }

    /**
     * Test that rapid submissions are flagged
     */
    public function test_rapid_submissions_are_flagged()
    {
        $auditService = new VoteAuditService();

        // Create multiple rapid submissions
        for ($i = 0; $i < 5; $i++) {
            $auditService->logVoteSubmission(
                $this->agent->id,
                $this->candidate->id,
                100 + $i,
                $i === 0 ? 'create' : 'update',
                'api'
            );
        }

        // Check that some submissions are flagged
        $flaggedCount = VoteAuditLog::where('is_flagged', true)->count();
        $this->assertGreaterThan(0, $flaggedCount);
    }

    /**
     * Test audit trail retrieval for agent
     */
    public function test_get_agent_audit_trail()
    {
        $auditService = new VoteAuditService();

        // Create multiple audit logs
        $auditService->logVoteSubmission($this->agent->id, $this->candidate->id, 100, 'create', 'api');
        $auditService->logVoteSubmission($this->agent->id, $this->candidate->id, 150, 'update', 'api');

        $auditTrail = $auditService->getAgentAuditTrail($this->agent->id);

        $this->assertCount(2, $auditTrail);
        $this->assertEquals(150, $auditTrail->first()->new_votes); // Most recent first
        $this->assertEquals(100, $auditTrail->last()->new_votes);
    }

    /**
     * Test suspicious activity summary
     */
    public function test_suspicious_activity_summary()
    {
        $auditService = new VoteAuditService();

        // Create some flagged submissions
        $auditLog1 = $auditService->logVoteSubmission($this->agent->id, $this->candidate->id, 1200, 'create', 'api');
        $auditLog2 = $auditService->logVoteSubmission($this->agent->id, $this->candidate->id, 1300, 'update', 'api');

        $summary = $auditService->getSuspiciousActivitySummary();

        $this->assertArrayHasKey('total_flagged', $summary);
        $this->assertArrayHasKey('large_changes', $summary);
        $this->assertArrayHasKey('rapid_submissions', $summary);
        $this->assertGreaterThan(0, $summary['total_flagged']);
    }

    /**
     * Test verification functionality
     */
    public function test_verify_submission()
    {
        $auditService = new VoteAuditService();
        $admin = User::create([
            'name' => 'Test Admin',
            'phone_number' => '+256700000998',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'is_active' => true
        ]);

        $auditLog = $auditService->logVoteSubmission(
            $this->agent->id,
            $this->candidate->id,
            100,
            'create',
            'api'
        );

        $this->actingAs($admin);

        $success = $auditService->verifySubmission($auditLog->id, 'Verified by admin');

        $this->assertTrue($success);
        
        $auditLog->refresh();
        $this->assertTrue($auditLog->is_verified);
        $this->assertEquals($admin->id, $auditLog->verified_by_user_id);
        $this->assertEquals('Verified by admin', $auditLog->verification_notes);
    }

    /**
     * Test flagging functionality
     */
    public function test_flag_submission()
    {
        $auditService = new VoteAuditService();
        $admin = User::create([
            'name' => 'Test Admin 2',
            'phone_number' => '+256700000997',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'is_active' => true
        ]);

        $auditLog = $auditService->logVoteSubmission(
            $this->agent->id,
            $this->candidate->id,
            100,
            'create',
            'api'
        );

        $this->actingAs($admin);

        $success = $auditService->flagSubmission($auditLog->id, 'manual_flag', 'Suspicious activity detected');

        $this->assertTrue($success);
        
        $auditLog->refresh();
        $this->assertTrue($auditLog->is_flagged);
        $this->assertEquals('manual_flag', $auditLog->flag_reason);
        $this->assertEquals('Suspicious activity detected', $auditLog->flag_notes);
        $this->assertEquals($admin->id, $auditLog->flagged_by_user_id);
    }

    /**
     * Test manager portal submissions are logged
     */
    public function test_manager_portal_submissions_logged()
    {
        $manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '+256700000996',
            'password' => bcrypt('password'),
            'user_type' => 'manager',
            'is_active' => true
        ]);
        $this->actingAs($manager);

        // Submit votes via manager portal
        $response = $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 200
            ]
        ]);

        $response->assertRedirect();

        // Check audit log
        $this->assertDatabaseHas('vote_audit_logs', [
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'new_votes' => 200,
            'submission_method' => 'manager_portal',
            'submitted_by_user_type' => 'manager'
        ]);
    }

    /**
     * Test audit dashboard access
     */
    public function test_audit_dashboard_access()
    {
        $admin = User::create([
            'name' => 'Test Admin 3',
            'phone_number' => '+256700000995',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'is_active' => true
        ]);
        $this->actingAs($admin);

        $response = $this->get('/admin/audit');

        $response->assertStatus(200);
        $response->assertSee('Vote Audit Dashboard');
    }

    /**
     * Test that non-admin users cannot access audit dashboard
     */
    public function test_non_admin_cannot_access_audit()
    {
        $this->actingAs($this->user); // Regular agent user

        $response = $this->get('/admin/audit');

        $response->assertRedirect();
        $response->assertSessionHas('error');
    }
}

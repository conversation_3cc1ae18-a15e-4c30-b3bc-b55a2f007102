<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SeededUsersTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the user types seeder
        $this->artisan('db:seed', ['--class' => 'UserTypesSeeder']);
    }

    /**
     * Test that admin user can login and access admin dashboard
     */
    public function test_admin_user_login_and_access()
    {
        $response = $this->post('/login', [
            'phone_number' => '+256700000000',
            'password' => 'admin123',
        ]);

        $response->assertRedirect('/home');
        $this->assertAuthenticated();
        
        $user = auth()->user();
        $this->assertEquals('admin', $user->user_type);
        $this->assertEquals('System Administrator', $user->name);
    }

    /**
     * Test that polling manager can login and access manager dashboard
     */
    public function test_polling_manager_login_and_access()
    {
        $response = $this->post('/login', [
            'phone_number' => '+256700000001',
            'password' => 'manager123',
        ]);

        $response->assertRedirect('/manager/dashboard');
        $this->assertAuthenticated();
        
        $user = auth()->user();
        $this->assertEquals('manager', $user->user_type);
        $this->assertEquals('Polling Station Manager', $user->name);
    }

    /**
     * Test that dashboard viewer can login and access dashboard
     */
    public function test_dashboard_viewer_login_and_access()
    {
        $response = $this->post('/login', [
            'phone_number' => '+256700000002',
            'password' => 'viewer123',
        ]);

        $response->assertRedirect('/home');
        $this->assertAuthenticated();
        
        $user = auth()->user();
        $this->assertEquals('viewer', $user->user_type);
        $this->assertEquals('Dashboard Viewer', $user->name);
    }

    /**
     * Test that all seeded users have correct roles
     */
    public function test_seeded_users_have_correct_roles()
    {
        // Test admin user
        $admin = User::where('phone_number', '+256700000000')->first();
        $this->assertNotNull($admin);
        $this->assertTrue($admin->hasRole('admin'));

        // Test polling manager
        $manager = User::where('phone_number', '+256700000001')->first();
        $this->assertNotNull($manager);
        $this->assertTrue($manager->hasRole('polling_station_manager'));

        // Test dashboard viewer
        $viewer = User::where('phone_number', '+256700000002')->first();
        $this->assertNotNull($viewer);
        $this->assertTrue($viewer->hasRole('dashboard_viewer'));
    }

    /**
     * Test that additional managers were created
     */
    public function test_additional_managers_created()
    {
        $managerPhones = [
            '+256700000011', // District Manager - Oyam
            '+256700000012', // County Manager - Aber
            '+256700000013', // Regional Manager - Northern
        ];

        foreach ($managerPhones as $phone) {
            $manager = User::where('phone_number', $phone)->first();
            $this->assertNotNull($manager, "Manager with phone {$phone} should exist");
            $this->assertEquals('manager', $manager->user_type);
            $this->assertTrue($manager->hasRole('polling_station_manager'));
        }
    }

    /**
     * Test that additional viewers were created
     */
    public function test_additional_viewers_created()
    {
        $viewerPhones = [
            '+256700000021', // Election Observer - EU
            '+256700000022', // Media Representative - NTV
            '+256700000023', // Party Agent - NRM
            '+256700000024', // Party Agent - NUP
            '+256700000025', // Civil Society Observer
            '+256700000026', // Government Official
        ];

        foreach ($viewerPhones as $phone) {
            $viewer = User::where('phone_number', $phone)->first();
            $this->assertNotNull($viewer, "Viewer with phone {$phone} should exist");
            $this->assertEquals('viewer', $viewer->user_type);
            $this->assertTrue($viewer->hasRole('dashboard_viewer'));
        }
    }

    /**
     * Test that all users are active
     */
    public function test_all_seeded_users_are_active()
    {
        $allPhones = [
            '+256700000000', // Admin
            '+256700000001', // Manager
            '+256700000002', // Viewer
            '+256700000011', '+256700000012', '+256700000013', // Additional managers
            '+256700000021', '+256700000022', '+256700000023', 
            '+256700000024', '+256700000025', '+256700000026', // Additional viewers
        ];

        foreach ($allPhones as $phone) {
            $user = User::where('phone_number', $phone)->first();
            $this->assertNotNull($user, "User with phone {$phone} should exist");
            $this->assertTrue($user->is_active, "User {$phone} should be active");
        }
    }

    /**
     * Test that manager can access manager dashboard after login
     */
    public function test_manager_dashboard_access_after_login()
    {
        // Login as manager
        $this->post('/login', [
            'phone_number' => '+256700000001',
            'password' => 'manager123',
        ]);

        // Access manager dashboard
        $response = $this->get('/manager/dashboard');
        $response->assertStatus(200);
        $response->assertSee('Polling Manager Dashboard');
    }

    /**
     * Test that viewer cannot access manager dashboard
     */
    public function test_viewer_cannot_access_manager_dashboard()
    {
        // Login as viewer
        $this->post('/login', [
            'phone_number' => '+256700000002',
            'password' => 'viewer123',
        ]);

        // Try to access manager dashboard
        $response = $this->get('/manager/dashboard');
        $response->assertRedirect('/home');
        $response->assertSessionHas('error');
    }
}

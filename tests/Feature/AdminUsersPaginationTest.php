<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AdminUsersPaginationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that admin users page displays pagination correctly
     */
    public function test_admin_users_page_displays_pagination()
    {
        // Create an admin user
        $admin = User::create([
            'name' => 'Admin User',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'is_active' => true,
        ]);

        // Create multiple users to trigger pagination (more than 15)
        for ($i = 1; $i <= 20; $i++) {
            User::create([
                'name' => "Test User {$i}",
                'phone_number' => "070000000{$i}",
                'password' => Hash::make('password'),
                'user_type' => 'viewer',
                'is_active' => true,
            ]);
        }

        // Login as admin
        $this->actingAs($admin);

        // Access admin users page
        $response = $this->get('/admin/users');

        // Should be successful
        $response->assertStatus(200);

        // Should contain pagination elements
        $response->assertSee('Showing');
        $response->assertSee('to');
        $response->assertSee('of');
        $response->assertSee('users');

        // Should have pagination links
        $response->assertSee('page=2');
    }

    /**
     * Test that pagination preserves search filters
     */
    public function test_pagination_preserves_search_filters()
    {
        // Create an admin user
        $admin = User::create([
            'name' => 'Admin User',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'is_active' => true,
        ]);

        // Create multiple users with specific names to test search
        for ($i = 1; $i <= 20; $i++) {
            User::create([
                'name' => "Search User {$i}",
                'phone_number' => "070000000{$i}",
                'password' => Hash::make('password'),
                'user_type' => 'viewer',
                'is_active' => true,
            ]);
        }

        // Login as admin
        $this->actingAs($admin);

        // Access admin users page with search filter
        $response = $this->get('/admin/users?search=Search');

        // Should be successful
        $response->assertStatus(200);

        // Should preserve search parameter in pagination links
        $response->assertSee('search=Search');
    }

    /**
     * Test that users can navigate between pages
     */
    public function test_users_can_navigate_between_pages()
    {
        // Create an admin user
        $admin = User::create([
            'name' => 'Admin User',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'is_active' => true,
        ]);

        // Create multiple users to trigger pagination
        for ($i = 1; $i <= 30; $i++) {
            User::create([
                'name' => "Test User {$i}",
                'phone_number' => "070000000{$i}",
                'password' => Hash::make('password'),
                'user_type' => 'viewer',
                'is_active' => true,
            ]);
        }

        // Login as admin
        $this->actingAs($admin);

        // Access page 2
        $response = $this->get('/admin/users?page=2');

        // Should be successful
        $response->assertStatus(200);

        // Should show different users on page 2
        $response->assertSee('Showing 16 to');
    }

    /**
     * Test that per-page selector works correctly
     */
    public function test_per_page_selector_works()
    {
        // Create an admin user
        $admin = User::create([
            'name' => 'Admin User',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'is_active' => true,
        ]);

        // Create multiple users
        for ($i = 1; $i <= 30; $i++) {
            User::create([
                'name' => "Test User {$i}",
                'phone_number' => "070000000{$i}",
                'password' => Hash::make('password'),
                'user_type' => 'viewer',
                'is_active' => true,
            ]);
        }

        // Login as admin
        $this->actingAs($admin);

        // Test with 25 per page
        $response = $this->get('/admin/users?per_page=25');

        // Should be successful
        $response->assertStatus(200);

        // Should show 25 users on first page (plus the admin = 26 total on page 1)
        $response->assertSee('Showing 1 to 25');
    }
}

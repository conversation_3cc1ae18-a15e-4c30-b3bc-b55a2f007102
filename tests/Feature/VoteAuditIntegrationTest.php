<?php

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\Candidate;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\User;
use App\Models\Vote;
use App\Models\VoteAuditLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VoteAuditIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $candidate;
    protected $position;
    protected $pollingStation;
    protected $user;
    protected $manager;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '+256700000999',
            'password' => bcrypt('password'),
            'user_type' => 'agent',
            'is_active' => true
        ]);

        $this->manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '+256700000998',
            'password' => bcrypt('password'),
            'user_type' => 'manager',
            'is_active' => true
        ]);

        $this->pollingStation = PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District'
        ]);

        $this->agent = Agent::create([
            'user_id' => $this->user->id,
            'polling_station_id' => $this->pollingStation->id
        ]);

        $this->position = Position::create([
            'name' => 'President'
        ]);

        $this->candidate = Candidate::create([
            'name' => 'Test Candidate',
            'position_id' => $this->position->id
        ]);
    }

    /**
     * Test that manager portal submissions create audit logs
     */
    public function test_manager_portal_creates_audit_logs()
    {
        $this->actingAs($this->manager);

        // Submit votes via manager portal
        $response = $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 250
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that vote was created
        $this->assertDatabaseHas('votes', [
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'number_of_votes' => 250
        ]);

        // Check that audit log was created
        $this->assertDatabaseHas('vote_audit_logs', [
            'agent_id' => $this->agent->id,
            'candidate_id' => $this->candidate->id,
            'new_votes' => 250,
            'submission_method' => 'manager_portal',
            'submitted_by_user_type' => 'manager'
        ]);

        $auditLog = VoteAuditLog::first();
        $this->assertEquals('create', $auditLog->action_type);
        $this->assertEquals(0, $auditLog->previous_votes);
        $this->assertEquals(250, $auditLog->vote_difference);
    }

    /**
     * Test that multiple submissions are properly tracked
     */
    public function test_multiple_submissions_tracked()
    {
        $this->actingAs($this->manager);

        // First submission
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 100
            ]
        ]);

        // Second submission (update)
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 175
            ]
        ]);

        // Third submission (another update)
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 200
            ]
        ]);

        // Should have 3 audit log entries
        $auditLogs = VoteAuditLog::orderBy('submission_time', 'asc')->get();
        $this->assertCount(3, $auditLogs);

        // Check first submission
        $this->assertEquals('create', $auditLogs[0]->action_type);
        $this->assertEquals(0, $auditLogs[0]->previous_votes);
        $this->assertEquals(100, $auditLogs[0]->new_votes);
        $this->assertEquals(100, $auditLogs[0]->vote_difference);

        // Check second submission
        $this->assertEquals('update', $auditLogs[1]->action_type);
        $this->assertEquals(100, $auditLogs[1]->previous_votes);
        $this->assertEquals(175, $auditLogs[1]->new_votes);
        $this->assertEquals(75, $auditLogs[1]->vote_difference);

        // Check third submission
        $this->assertEquals('update', $auditLogs[2]->action_type);
        $this->assertEquals(175, $auditLogs[2]->previous_votes);
        $this->assertEquals(200, $auditLogs[2]->new_votes);
        $this->assertEquals(25, $auditLogs[2]->vote_difference);

        // Final vote count should be 200
        $vote = Vote::where('agent_id', $this->agent->id)
            ->where('candidate_id', $this->candidate->id)
            ->first();
        $this->assertEquals(200, $vote->number_of_votes);
    }

    /**
     * Test that large vote changes are flagged
     */
    public function test_large_vote_changes_flagged()
    {
        $this->actingAs($this->manager);

        // Submit a large vote count that should be flagged
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 1500 // Large number should trigger flag
            ]
        ]);

        // Check that audit log was flagged
        $auditLog = VoteAuditLog::first();
        $this->assertTrue($auditLog->is_flagged);
        $this->assertEquals('large_vote_change', $auditLog->flag_reason);
    }

    /**
     * Test audit dashboard access
     */
    public function test_audit_dashboard_shows_submissions()
    {
        // Create some audit data
        $this->actingAs($this->manager);
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 150
            ]
        ]);

        // Switch to admin user
        $admin = User::create([
            'name' => 'Test Admin',
            'phone_number' => '+256700000997',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'is_active' => true
        ]);

        $this->actingAs($admin);

        // Access audit dashboard
        $response = $this->get('/admin/audit');
        $response->assertStatus(200);
        $response->assertSee('Vote Audit Dashboard');
        $response->assertSee('Test Candidate');
        $response->assertSee('Test Polling Station');
        $response->assertSee('150'); // Vote count
    }

    /**
     * Test that audit logs contain all required information
     */
    public function test_audit_logs_contain_complete_information()
    {
        $this->actingAs($this->manager);

        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 300
            ]
        ]);

        $auditLog = VoteAuditLog::with([
            'agent.user',
            'candidate.position',
            'pollingStation',
            'submittedBy'
        ])->first();

        // Check all relationships are loaded
        $this->assertNotNull($auditLog->agent);
        $this->assertNotNull($auditLog->agent->user);
        $this->assertNotNull($auditLog->candidate);
        $this->assertNotNull($auditLog->candidate->position);
        $this->assertNotNull($auditLog->pollingStation);
        $this->assertNotNull($auditLog->submittedBy);

        // Check data integrity
        $this->assertEquals($this->agent->id, $auditLog->agent_id);
        $this->assertEquals($this->candidate->id, $auditLog->candidate_id);
        $this->assertEquals($this->pollingStation->id, $auditLog->polling_station_id);
        $this->assertEquals($this->position->id, $auditLog->position_id);
        $this->assertEquals($this->manager->id, $auditLog->submitted_by_user_id);
        $this->assertEquals('manager', $auditLog->submitted_by_user_type);
        $this->assertEquals('manager_portal', $auditLog->submission_method);
    }

    /**
     * Test that vote decreases are properly tracked
     */
    public function test_vote_decreases_tracked()
    {
        $this->actingAs($this->manager);

        // Initial submission
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 500
            ]
        ]);

        // Decrease votes
        $this->post("/manager/station/{$this->pollingStation->id}/submit-votes", [
            'votes' => [
                $this->candidate->id => 350
            ]
        ]);

        $auditLogs = VoteAuditLog::orderBy('submission_time', 'asc')->get();
        $this->assertCount(2, $auditLogs);

        // Check the decrease is properly recorded
        $decreaseLog = $auditLogs[1];
        $this->assertEquals(500, $decreaseLog->previous_votes);
        $this->assertEquals(350, $decreaseLog->new_votes);
        $this->assertEquals(-150, $decreaseLog->vote_difference); // Negative difference
    }
}
